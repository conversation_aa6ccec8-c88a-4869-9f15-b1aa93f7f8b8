"""
Chat service for processing user queries and generating responses.
Integrates vector retrieval, intent classification, and response generation.
"""

import time
import uuid
from typing import Dict, Any, List, Optional
from src.utils.logger import get_logger
from src.middleware.logging_middleware import log_retrieval_performance
from src.chain.chain_builder import ChainBuilder
from src.conversation.language_detector import detect_language

logger = get_logger(__name__)


class ChatService:
    """Service for handling chat interactions and response generation."""
    
    def __init__(self):
        """Initialize the chat service with required components."""
        try:
            self.chain_builder = ChainBuilder()
            logger.info("ChatService initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize ChatService: {e}", exc_info=True)
            raise
    
    async def process_query(
        self,
        query: str,
        device_id: str,
        response_mode: str = "detailed",
        email: Optional[str] = None,
        employee_id: Optional[str] = None,
        files_info: Optional[List[Dict[str, Any]]] = None,
        month: Optional[str] = None,
        year: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process a user query and generate a response.
        
        Args:
            query: User's question/query
            device_id: Unique identifier for the user/device
            response_mode: "detailed" or "concise"
            email: User's email (optional)
            employee_id: User's employee ID (optional)
            files_info: Information about uploaded files (optional)
            month: Month for payroll queries (optional)
            year: Year for payroll queries (optional)
            
        Returns:
            Dictionary containing response and metadata
        """
        start_time = time.time()
        chat_id = str(uuid.uuid4())
        
        try:
            logger.info(f"🚀 Processing query for device {device_id}: '{query[:100]}...'")
            
            # Detect language
            language = detect_language(query)
            logger.info(f"🌐 Detected language: {language}")
            
            # Process the query through the chain builder
            result = await self.chain_builder.run_chain(
                query=query,
                device_id=device_id,
                files_info=files_info or [],
                response_mode=response_mode,
                email=email,
                employee_id=employee_id,
                month=month,
                year=year
            )
            
            processing_time = time.time() - start_time
            
            # Extract and format the response
            response_data = {
                'chat_id': chat_id,
                'response': result.get('response', 'I apologize, but I encountered an issue processing your request.'),
                'sources': result.get('sources', []),
                'sources_count': len(result.get('sources', [])),
                'intent': result.get('intent'),
                'confidence': result.get('confidence'),
                'language': language,
                'response_mode': response_mode,
                'processing_time': round(processing_time, 3),
                'timestamp': time.time(),
                'device_id': device_id,
                'status': 'success'
            }
            
            # Add retrieval metrics if available
            if 'retrieval_time' in result:
                response_data['retrieval_time'] = result['retrieval_time']
            if 'embedding_time' in result:
                response_data['embedding_time'] = result['embedding_time']
            
            # Log retrieval performance
            if 'sources' in result:
                log_retrieval_performance(
                    query=query,
                    results_count=len(result['sources']),
                    search_time=result.get('retrieval_time', 0),
                    embedding_time=result.get('embedding_time', 0)
                )
            
            logger.info(f"✅ Query processed successfully in {processing_time:.3f}s", extra={
                'device_id': device_id,
                'processing_time': processing_time,
                'sources_found': len(result.get('sources', [])),
                'intent': result.get('intent'),
                'language': language
            })
            
            return response_data
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_message = f"Failed to process query: {str(e)}"
            
            logger.error(f"❌ Error processing query: {e}", extra={
                'device_id': device_id,
                'query_preview': query[:100],
                'processing_time': processing_time,
                'error_type': type(e).__name__
            }, exc_info=True)
            
            # Return error response
            return {
                'chat_id': chat_id,
                'response': 'I apologize, but I encountered an issue processing your request. Please try again or contact support if the problem persists.',
                'sources': [],
                'sources_count': 0,
                'intent': None,
                'confidence': 0.0,
                'language': detect_language(query),
                'response_mode': response_mode,
                'processing_time': round(processing_time, 3),
                'timestamp': time.time(),
                'device_id': device_id,
                'status': 'error',
                'error': error_message
            }
    
    def get_service_health(self) -> Dict[str, Any]:
        """Get the health status of the chat service."""
        try:
            # Test basic functionality
            health_data = {
                'service': 'ChatService',
                'status': 'healthy',
                'timestamp': time.time(),
                'components': {
                    'chain_builder': 'healthy' if self.chain_builder else 'unhealthy',
                }
            }
            
            # Test chain builder
            if self.chain_builder:
                try:
                    # Quick health check - this should be fast
                    health_data['components']['chain_builder'] = 'healthy'
                except Exception as e:
                    health_data['components']['chain_builder'] = f'unhealthy: {str(e)}'
                    health_data['status'] = 'degraded'
            
            return health_data
            
        except Exception as e:
            logger.error(f"Error checking service health: {e}", exc_info=True)
            return {
                'service': 'ChatService',
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': time.time()
            }
    
    async def generate_followup_question(
        self,
        selected_text: str,
        full_assistant_message: str,
        previous_user_query: str = "",
        device_id: str = "unknown",
        language: str = "en"
    ) -> Dict[str, Any]:
        """Generate a follow-up question based on selected text."""
        try:
            logger.info(f"🔄 Generating follow-up question for device {device_id}")
            
            # This would integrate with your existing follow-up generation logic
            # For now, return a simple response
            followup_question = f"Can you tell me more about: {selected_text[:50]}...?"
            
            return {
                'followup_question': followup_question,
                'selected_text': selected_text,
                'device_id': device_id,
                'language': language,
                'timestamp': time.time(),
                'status': 'success'
            }
            
        except Exception as e:
            logger.error(f"Error generating follow-up question: {e}", exc_info=True)
            return {
                'followup_question': "What else would you like to know?",
                'error': str(e),
                'status': 'error',
                'timestamp': time.time()
            }
