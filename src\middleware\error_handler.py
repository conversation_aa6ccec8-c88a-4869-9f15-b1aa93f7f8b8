"""
Centralized error handling middleware for the Flask application.
"""

import traceback
import time
from flask import jsonify, request, g
from werkzeug.exceptions import HTTPException
from src.utils.logger import get_logger

logger = get_logger(__name__)


def setup_error_handlers(app):
    """Setup centralized error handling for the Flask app."""
    
    @app.errorhandler(400)
    def bad_request(error):
        """Handle 400 Bad Request errors."""
        error_data = {
            'error_type': 'BAD_REQUEST',
            'status_code': 400,
            'error_message': str(error),
            'request_id': getattr(g, 'request_id', 'unknown'),
            'path': request.path,
            'method': request.method,
        }
        logger.warning("❌ Bad Request", extra=error_data)
        return jsonify({
            'error': 'Bad Request',
            'message': 'The request was invalid or malformed',
            'status_code': 400,
            'request_id': error_data['request_id']
        }), 400
    
    @app.errorhandler(401)
    def unauthorized(error):
        """Handle 401 Unauthorized errors."""
        error_data = {
            'error_type': 'UNAUTHORIZED',
            'status_code': 401,
            'error_message': str(error),
            'request_id': getattr(g, 'request_id', 'unknown'),
            'path': request.path,
            'method': request.method,
        }
        logger.warning("❌ Unauthorized access attempt", extra=error_data)
        return jsonify({
            'error': 'Unauthorized',
            'message': 'Authentication required',
            'status_code': 401,
            'request_id': error_data['request_id']
        }), 401
    
    @app.errorhandler(403)
    def forbidden(error):
        """Handle 403 Forbidden errors."""
        error_data = {
            'error_type': 'FORBIDDEN',
            'status_code': 403,
            'error_message': str(error),
            'request_id': getattr(g, 'request_id', 'unknown'),
            'path': request.path,
            'method': request.method,
        }
        logger.warning("❌ Forbidden access attempt", extra=error_data)
        return jsonify({
            'error': 'Forbidden',
            'message': 'Insufficient permissions',
            'status_code': 403,
            'request_id': error_data['request_id']
        }), 403
    
    @app.errorhandler(404)
    def not_found(error):
        """Handle 404 Not Found errors."""
        error_data = {
            'error_type': 'NOT_FOUND',
            'status_code': 404,
            'error_message': str(error),
            'request_id': getattr(g, 'request_id', 'unknown'),
            'path': request.path,
            'method': request.method,
        }
        logger.info("❌ Resource not found", extra=error_data)
        return jsonify({
            'error': 'Not Found',
            'message': 'The requested resource was not found',
            'status_code': 404,
            'request_id': error_data['request_id']
        }), 404
    
    @app.errorhandler(429)
    def rate_limit_exceeded(error):
        """Handle 429 Rate Limit Exceeded errors."""
        error_data = {
            'error_type': 'RATE_LIMIT_EXCEEDED',
            'status_code': 429,
            'error_message': str(error),
            'request_id': getattr(g, 'request_id', 'unknown'),
            'path': request.path,
            'method': request.method,
            'client_ip': request.remote_addr,
        }
        logger.warning("❌ Rate limit exceeded", extra=error_data)
        return jsonify({
            'error': 'Rate Limit Exceeded',
            'message': 'Too many requests. Please try again later.',
            'status_code': 429,
            'request_id': error_data['request_id']
        }), 429
    
    @app.errorhandler(500)
    def internal_server_error(error):
        """Handle 500 Internal Server Error."""
        error_data = {
            'error_type': 'INTERNAL_SERVER_ERROR',
            'status_code': 500,
            'error_message': str(error),
            'request_id': getattr(g, 'request_id', 'unknown'),
            'path': request.path,
            'method': request.method,
            'traceback': traceback.format_exc(),
        }
        logger.error("❌ Internal server error", extra=error_data)
        return jsonify({
            'error': 'Internal Server Error',
            'message': 'An unexpected error occurred. Please try again later.',
            'status_code': 500,
            'request_id': error_data['request_id']
        }), 500
    
    @app.errorhandler(502)
    def bad_gateway(error):
        """Handle 502 Bad Gateway errors."""
        error_data = {
            'error_type': 'BAD_GATEWAY',
            'status_code': 502,
            'message': str(error),
            'request_id': getattr(g, 'request_id', 'unknown'),
            'path': request.path,
            'method': request.method,
        }
        logger.error("❌ Bad gateway error", extra=error_data)
        return jsonify({
            'error': 'Bad Gateway',
            'message': 'Upstream service unavailable',
            'status_code': 502,
            'request_id': error_data['request_id']
        }), 502
    
    @app.errorhandler(503)
    def service_unavailable(error):
        """Handle 503 Service Unavailable errors."""
        error_data = {
            'error_type': 'SERVICE_UNAVAILABLE',
            'status_code': 503,
            'error_message': str(error),
            'request_id': getattr(g, 'request_id', 'unknown'),
            'path': request.path,
            'method': request.method,
        }
        logger.error("❌ Service unavailable", extra=error_data)
        return jsonify({
            'error': 'Service Unavailable',
            'message': 'Service is temporarily unavailable. Please try again later.',
            'status_code': 503,
            'request_id': error_data['request_id']
        }), 503
    
    @app.errorhandler(Exception)
    def handle_unexpected_error(error):
        """Handle any unexpected errors."""
        error_data = {
            'error_type': 'UNEXPECTED_ERROR',
            'error_class': error.__class__.__name__,
            'error_message': str(error),
            'request_id': getattr(g, 'request_id', 'unknown'),
            'path': request.path if request else 'unknown',
            'method': request.method if request else 'unknown',
            'traceback': traceback.format_exc(),
            'timestamp': time.time(),
        }
        logger.critical("💥 Unexpected error occurred", extra=error_data)
        
        # Return appropriate response based on error type
        if isinstance(error, HTTPException):
            return jsonify({
                'error': error.name,
                'message': error.description,
                'status_code': error.code,
                'request_id': error_data['request_id']
            }), error.code
        else:
            return jsonify({
                'error': 'Unexpected Error',
                'message': 'An unexpected error occurred. Please contact support.',
                'status_code': 500,
                'request_id': error_data['request_id']
            }), 500
