"""
Chat API routes for the HR Assistant Chatbot.
Handles chat interactions, message processing, and conversation management.
"""

import time
import asyncio
from flask import Blueprint, request, jsonify, g
from src.utils.logger import get_logger
from src.middleware.logging_middleware import log_user_activity, log_chat_interaction, require_logging
from src.services.chat_service import ChatService
from src.database.conversation_store import ConversationStore

logger = get_logger(__name__)
chat_bp = Blueprint('chat', __name__)

# Initialize services
chat_service = ChatService()
conversation_store = ConversationStore()


@chat_bp.route('/query', methods=['POST'])
@require_logging('CHAT_QUERY')
def chat_query():
    """
    Main chat endpoint for processing user queries.
    Expected payload: {
        "query": "user question",
        "device_id": "unique_device_id",
        "response_mode": "detailed|concise",
        "email": "<EMAIL>",
        "employee_id": "EMP123",
        "files_info": [{"name": "file.pdf", "size": 1024, "type": "application/pdf"}]
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400
        
        # Extract required fields
        query = data.get('query', '').strip()
        device_id = data.get('device_id', 'unknown')
        
        if not query:
            return jsonify({'error': 'Query is required'}), 400
        
        # Extract optional fields
        response_mode = data.get('response_mode', 'detailed')
        email = data.get('email')
        employee_id = data.get('employee_id')
        files_info = data.get('files_info', [])
        
        # Log user activity
        log_user_activity('CHAT_QUERY_RECEIVED', {
            'device_id': device_id,
            'query_length': len(query),
            'response_mode': response_mode,
            'has_files': len(files_info) > 0,
            'email': email,
            'employee_id': employee_id
        })
        
        # Process the chat query
        start_time = time.time()
        
        # Run the async chat processing
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(
                chat_service.process_query(
                    query=query,
                    device_id=device_id,
                    response_mode=response_mode,
                    email=email,
                    employee_id=employee_id,
                    files_info=files_info
                )
            )
        finally:
            loop.close()
        
        response_time = time.time() - start_time
        
        # Log chat interaction
        log_chat_interaction(
            query=query,
            response=result.get('response', ''),
            device_id=device_id,
            retrieval_results=result.get('sources_count', 0),
            intent=result.get('intent'),
            language=result.get('language'),
            response_time=response_time
        )
        
        # Save conversation to database
        conversation_store.save_conversation(
            chat_id=result.get('chat_id', device_id),
            user_query=query,
            assistant_response=result.get('response', ''),
            language=result.get('language', 'en'),
            device_id=device_id,
            query_start_time=start_time,
            response_end_time=time.time(),
            intent=result.get('intent')
        )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error processing chat query: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to process query',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@chat_bp.route('/chat-history/<device_id>', methods=['GET'])
@require_logging('CHAT_HISTORY')
def get_chat_history(device_id):
    """Get chat history for a specific device/user."""
    try:
        # Get query parameters
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # Validate limit
        if limit > 100:
            limit = 100
        
        log_user_activity('CHAT_HISTORY_REQUEST', {
            'device_id': device_id,
            'limit': limit,
            'offset': offset,
            'start_date': start_date,
            'end_date': end_date
        })
        
        # Get conversations from database
        conversations = conversation_store.get_all_conversations(
            device_id=device_id,
            start_date=start_date,
            end_date=end_date,
            limit=limit,
            offset=offset
        )
        
        return jsonify({
            'conversations': conversations,
            'total': len(conversations),
            'limit': limit,
            'offset': offset,
            'device_id': device_id
        })
        
    except Exception as e:
        logger.error(f"Error retrieving chat history: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve chat history',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@chat_bp.route('/chat-sessions', methods=['GET'])
@require_logging('CHAT_SESSIONS')
def get_chat_sessions():
    """Get all chat sessions with pagination."""
    try:
        # Get query parameters
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # Validate limit
        if limit > 100:
            limit = 100
        
        log_user_activity('CHAT_SESSIONS_REQUEST', {
            'limit': limit,
            'offset': offset,
            'start_date': start_date,
            'end_date': end_date
        })
        
        # Get all conversations
        conversations = conversation_store.get_all_conversations(
            start_date=start_date,
            end_date=end_date,
            limit=limit,
            offset=offset
        )
        
        return jsonify({
            'sessions': conversations,
            'total': len(conversations),
            'limit': limit,
            'offset': offset
        })
        
    except Exception as e:
        logger.error(f"Error retrieving chat sessions: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve chat sessions',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@chat_bp.route('/chat-sessions/<session_id>', methods=['GET'])
@require_logging('CHAT_SESSION_DETAIL')
def get_chat_session(session_id):
    """Get details for a specific chat session."""
    try:
        log_user_activity('CHAT_SESSION_DETAIL_REQUEST', {
            'session_id': session_id
        })
        
        # Get conversation by ID (assuming session_id maps to device_id)
        conversations = conversation_store.get_all_conversations(
            device_id=session_id,
            limit=1
        )
        
        if not conversations:
            return jsonify({'error': 'Session not found'}), 404
        
        return jsonify({
            'session': conversations[0],
            'session_id': session_id
        })
        
    except Exception as e:
        logger.error(f"Error retrieving chat session: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve chat session',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@chat_bp.route('/chat-sessions/<session_id>/messages', methods=['GET'])
@require_logging('CHAT_MESSAGES')
def get_chat_messages(session_id):
    """Get all messages for a specific chat session."""
    try:
        log_user_activity('CHAT_MESSAGES_REQUEST', {
            'session_id': session_id
        })
        
        # Get all conversations for this session
        conversations = conversation_store.get_all_conversations(
            device_id=session_id,
            limit=100
        )
        
        # Format as messages
        messages = []
        for conv in conversations:
            messages.extend([
                {
                    'type': 'user',
                    'content': conv['user_query'],
                    'timestamp': conv['query_timestamp']
                },
                {
                    'type': 'assistant',
                    'content': conv['assistant_response'],
                    'timestamp': conv['response_timestamp'],
                    'intent': conv.get('intent'),
                    'language': conv.get('language')
                }
            ])
        
        return jsonify({
            'messages': messages,
            'session_id': session_id,
            'total_messages': len(messages)
        })
        
    except Exception as e:
        logger.error(f"Error retrieving chat messages: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve chat messages',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@chat_bp.route('/generate-followup', methods=['POST'])
@require_logging('GENERATE_FOLLOWUP')
def generate_followup_question():
    """Generate a follow-up question based on selected text."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400

        # Extract required fields
        selected_text = data.get('selected_text', '').strip()
        full_assistant_message = data.get('full_assistant_message', '')
        previous_user_query = data.get('previous_user_query', '')
        device_id = data.get('device_id', 'unknown')
        language = data.get('language', 'en')

        if not selected_text:
            return jsonify({'error': 'Selected text is required'}), 400

        log_user_activity('FOLLOWUP_GENERATION_REQUEST', {
            'device_id': device_id,
            'selected_text_length': len(selected_text),
            'language': language
        })

        # Generate follow-up question
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(
                chat_service.generate_followup_question(
                    selected_text=selected_text,
                    full_assistant_message=full_assistant_message,
                    previous_user_query=previous_user_query,
                    device_id=device_id,
                    language=language
                )
            )
        finally:
            loop.close()

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error generating follow-up question: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to generate follow-up question',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@chat_bp.route('/chat-types', methods=['GET'])
@require_logging('CHAT_TYPES')
def get_chat_types():
    """Get available chat types and categories."""
    try:
        log_user_activity('CHAT_TYPES_REQUEST')

        chat_types = [
            {'id': 'general', 'name': 'General HR Questions', 'description': 'General HR policy and procedure questions'},
            {'id': 'payroll', 'name': 'Payroll & Benefits', 'description': 'Salary, benefits, and payroll-related queries'},
            {'id': 'leave', 'name': 'Leave & Time Off', 'description': 'Vacation, sick leave, and time-off policies'},
            {'id': 'policy', 'name': 'Company Policies', 'description': 'Company policies and guidelines'},
            {'id': 'technical', 'name': 'Technical Support', 'description': 'IT and technical assistance'},
        ]

        return jsonify({
            'chat_types': chat_types,
            'total': len(chat_types),
            'timestamp': time.time()
        })

    except Exception as e:
        logger.error(f"Error retrieving chat types: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve chat types',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500
