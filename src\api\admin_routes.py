"""
Admin API routes for the HR Assistant Chatbot.
Provides administrative functions, user management, and system controls.
"""

import time
from flask import Blueprint, request, jsonify, g
from src.utils.logger import get_logger
from src.middleware.logging_middleware import log_user_activity, require_logging
from src.services.admin_service import AdminService

logger = get_logger(__name__)
admin_bp = Blueprint('admin', __name__)

# Initialize admin service
admin_service = AdminService()


@admin_bp.route('/admin/users', methods=['GET'])
@require_logging('ADMIN_USERS_LIST')
def get_admin_users():
    """Get list of admin users with pagination."""
    try:
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        role = request.args.get('role')
        
        if limit > 100:
            limit = 100
        
        log_user_activity('ADMIN_USERS_LIST_REQUEST', {
            'limit': limit,
            'offset': offset,
            'role_filter': role
        })
        
        users_data = admin_service.get_admin_users(limit, offset, role)
        
        return jsonify(users_data)
        
    except Exception as e:
        logger.error(f"Error retrieving admin users: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve admin users',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@admin_bp.route('/admin/users', methods=['POST'])
@require_logging('ADMIN_USER_CREATE')
def create_admin_user():
    """Create a new admin user."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400
        
        required_fields = ['email', 'full_name', 'role']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400
        
        log_user_activity('ADMIN_USER_CREATE_REQUEST', {
            'email': data['email'],
            'role': data['role']
        })
        
        result = admin_service.create_admin_user(
            email=data['email'],
            full_name=data['full_name'],
            role=data['role'],
            tenant_id=data.get('tenant_id')
        )
        
        if result.get('success'):
            return jsonify(result), 201
        else:
            return jsonify(result), 400
        
    except Exception as e:
        logger.error(f"Error creating admin user: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to create admin user',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@admin_bp.route('/admin/users/<user_id>', methods=['PUT'])
@require_logging('ADMIN_USER_UPDATE')
def update_admin_user(user_id):
    """Update an admin user."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400
        
        log_user_activity('ADMIN_USER_UPDATE_REQUEST', {
            'user_id': user_id,
            'update_fields': list(data.keys())
        })
        
        result = admin_service.update_admin_user(user_id, data)
        
        if result.get('success'):
            return jsonify(result)
        else:
            return jsonify(result), 400
        
    except Exception as e:
        logger.error(f"Error updating admin user: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to update admin user',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@admin_bp.route('/admin/users/<user_id>/role', methods=['PUT'])
@require_logging('ADMIN_USER_ROLE_CHANGE')
def change_user_role(user_id):
    """Change a user's role."""
    try:
        data = request.get_json()
        if not data or not data.get('role'):
            return jsonify({'error': 'Role is required'}), 400
        
        log_user_activity('ADMIN_USER_ROLE_CHANGE_REQUEST', {
            'user_id': user_id,
            'new_role': data['role']
        })
        
        result = admin_service.change_user_role(user_id, data['role'])
        
        if result.get('success'):
            return jsonify(result)
        else:
            return jsonify(result), 400
        
    except Exception as e:
        logger.error(f"Error changing user role: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to change user role',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@admin_bp.route('/admin/settings', methods=['GET'])
@require_logging('ADMIN_SETTINGS_GET')
def get_admin_settings():
    """Get admin settings and configuration."""
    try:
        log_user_activity('ADMIN_SETTINGS_GET_REQUEST')
        
        settings = admin_service.get_admin_settings()
        
        return jsonify(settings)
        
    except Exception as e:
        logger.error(f"Error retrieving admin settings: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve admin settings',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@admin_bp.route('/admin/settings', methods=['PUT'])
@require_logging('ADMIN_SETTINGS_UPDATE')
def update_admin_settings():
    """Update admin settings."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400
        
        log_user_activity('ADMIN_SETTINGS_UPDATE_REQUEST', {
            'settings_keys': list(data.keys())
        })
        
        result = admin_service.update_admin_settings(data)
        
        if result.get('success'):
            return jsonify(result)
        else:
            return jsonify(result), 400
        
    except Exception as e:
        logger.error(f"Error updating admin settings: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to update admin settings',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@admin_bp.route('/admin/audit-logs', methods=['GET'])
@require_logging('ADMIN_AUDIT_LOGS')
def get_audit_logs():
    """Get audit logs with filtering."""
    try:
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        action_type = request.args.get('action_type')
        user_id = request.args.get('user_id')
        
        if limit > 100:
            limit = 100
        
        log_user_activity('ADMIN_AUDIT_LOGS_REQUEST', {
            'limit': limit,
            'offset': offset,
            'start_date': start_date,
            'end_date': end_date,
            'action_type': action_type,
            'user_id': user_id
        })
        
        audit_logs = admin_service.get_audit_logs(
            limit=limit,
            offset=offset,
            start_date=start_date,
            end_date=end_date,
            action_type=action_type,
            user_id=user_id
        )
        
        return jsonify(audit_logs)
        
    except Exception as e:
        logger.error(f"Error retrieving audit logs: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve audit logs',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@admin_bp.route('/admin/system-status', methods=['GET'])
@require_logging('ADMIN_SYSTEM_STATUS')
def get_system_status():
    """Get overall system status and health."""
    try:
        log_user_activity('ADMIN_SYSTEM_STATUS_REQUEST')
        
        system_status = admin_service.get_system_status()
        
        return jsonify(system_status)
        
    except Exception as e:
        logger.error(f"Error retrieving system status: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve system status',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@admin_bp.route('/admin/escalations', methods=['GET'])
@require_logging('ADMIN_ESCALATIONS')
def get_escalations():
    """Get escalated issues and tickets."""
    try:
        status = request.args.get('status', 'pending')
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        
        if limit > 100:
            limit = 100
        
        log_user_activity('ADMIN_ESCALATIONS_REQUEST', {
            'status': status,
            'limit': limit,
            'offset': offset
        })
        
        escalations = admin_service.get_escalations(status, limit, offset)
        
        return jsonify(escalations)
        
    except Exception as e:
        logger.error(f"Error retrieving escalations: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve escalations',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@admin_bp.route('/admin/cache/clear', methods=['POST'])
@require_logging('ADMIN_CACHE_CLEAR')
def clear_cache():
    """Clear system caches."""
    try:
        data = request.get_json() or {}
        cache_type = data.get('cache_type', 'all')
        
        log_user_activity('ADMIN_CACHE_CLEAR_REQUEST', {
            'cache_type': cache_type
        })
        
        result = admin_service.clear_cache(cache_type)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error clearing cache: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to clear cache',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500
