"""
Minimal main application entry point for testing basic functionality.
"""

from flask import Flask, jsonify
from flask_cors import CORS

def create_minimal_app():
    """Create a minimal Flask app for testing."""
    app = Flask(__name__)
    CORS(app, origins=["*"], supports_credentials=True)
    
    # Configure app settings
    app.config['SECRET_KEY'] = 'your-secret-key-here'
    app.config['DEBUG'] = False
    
    # Basic health check route
    @app.route('/health')
    def health_check():
        return jsonify({'status': 'healthy', 'message': 'HR Assistant Chatbot is running'})

    # API health check route (for frontend)
    @app.route('/api/health')
    def api_health_check():
        return jsonify({'status': 'healthy', 'message': 'HR Assistant Chatbot API is running'})

    # Basic root route
    @app.route('/')
    def root():
        return jsonify({'message': 'Welcome to HR Assistant Chatbot API'})
    
    return app


def main():
    """Main application entry point."""
    try:
        print("🚀 Starting minimal server...")

        # Create Flask app
        flask_app = create_minimal_app()
        print("✅ Flask app created successfully")

        print("🌐 Starting Flask server on port 5051...")
        flask_app.run(host="0.0.0.0", port=5051, debug=False)

    except Exception as e:
        print(f"❌ Error starting server: {e}")
        import traceback
        traceback.print_exc()
        raise


if __name__ == "__main__":
    main()
