"""
Main application entry point for the Advanced HR Assistant Chatbot.
Refactored for production-grade architecture with modular design.
"""

from flask import Flask, jsonify, request
from flask_cors import CORS

def main():
    """Main application entry point with simplified startup."""
    print("🚀 Starting server initialization...")

    app = Flask(__name__)
    CORS(app, origins=["*"], supports_credentials=True)

    # Configure app settings
    app.config['SECRET_KEY'] = 'your-secret-key-here'
    app.config['DEBUG'] = False

    # Try to register error handlers (with fallback)
    try:
        from src.middleware.error_handler import setup_error_handlers
        setup_error_handlers(app)
        print("✅ Error handlers registered successfully")
    except Exception as e:
        print(f"⚠️ Warning: Could not register error handlers: {e}")

    # Basic health check route
    @app.route('/health')
    def health_check():
        return jsonify({'status': 'healthy', 'message': 'HR Assistant Chatbot is running'})

    # API health check route (for frontend)
    @app.route('/api/health')
    def api_health_check():
        return jsonify({'status': 'healthy', 'message': 'HR Assistant Chatbot API is running'})

    # Authentication routes
    @app.route('/api/login', methods=['POST'])
    def api_login():
        data = request.get_json() or {}
        email = data.get('email', '')
        password = data.get('password', '')

        print(f"Login attempt: {email}")

        # Simple mock authentication for testing
        if email and password:
            return jsonify({
                'success': True,
                'message': 'Login successful',
                'user': {
                    'id': 1,
                    'email': email,
                    'name': 'Test User',
                    'role': 'user'
                },
                'access_token': 'mock_token_123',
                'token_type': 'Bearer'
            })
        else:
            return jsonify({'success': False, 'message': 'Invalid credentials'}), 401

    @app.route('/api/register', methods=['POST'])
    def api_register():
        data = request.get_json() or {}
        print(f"Register attempt: {data.get('email', '')}")
        return jsonify({
            'success': True,
            'message': 'Registration successful',
            'user': {
                'id': 2,
                'email': data.get('email', ''),
                'name': data.get('full_name', ''),
                'role': 'user'
            }
        })

    @app.route('/api/logout', methods=['POST'])
    def api_logout():
        return jsonify({'success': True, 'message': 'Logout successful'})

    # Chat routes
    @app.route('/api/query', methods=['POST'])
    def api_query():
        data = request.get_json() or {}
        query = data.get('query', '')

        print(f"Chat query: {query}")

        return jsonify({
            'success': True,
            'response': f'Mock response to: {query}',
            'intent': 'general',
            'confidence': 0.95,
            'session_id': 'mock_session_123'
        })

    @app.route('/api/chat/history', methods=['GET'])
    def api_chat_history():
        return jsonify({
            'success': True,
            'history': [],
            'total': 0
        })

    # Basic root route
    @app.route('/')
    def root():
        return jsonify({'message': 'Welcome to HR Assistant Chatbot API'})

    print("✅ Flask app created successfully")
    print("🌐 Starting Flask server on port 5051...")
    app.run(host="0.0.0.0", port=5051, debug=False)


if __name__ == "__main__":
    main()