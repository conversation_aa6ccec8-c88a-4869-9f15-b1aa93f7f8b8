"""
Main application entry point for the Advanced HR Assistant Chatbot.
Refactored for production-grade architecture with modular design.
"""

import sys
import os

def main():
    """Main application entry point with simplified startup."""
    try:
        print("🚀 Starting server initialization...")

        # Create Flask app with minimal dependencies
        from flask import Flask, jsonify
        from flask_cors import CORS

        app = Flask(__name__)
        CORS(app, origins=["*"], supports_credentials=True)

        # Configure app settings
        app.config['SECRET_KEY'] = 'your-secret-key-here'
        app.config['DEBUG'] = False

        # Try to register error handlers (with fallback)
        try:
            from src.middleware.error_handler import setup_error_handlers
            setup_error_handlers(app)
            print("✅ Error handlers registered successfully")
        except Exception as e:
            print(f"⚠️ Warning: Could not register error handlers: {e}")

        # Basic health check route
        @app.route('/health')
        def health_check():
            return jsonify({'status': 'healthy', 'message': 'HR Assistant Chatbot is running'})

        # API health check route (for frontend)
        @app.route('/api/health')
        def api_health_check():
            return jsonify({'status': 'healthy', 'message': 'HR Assistant Chatbot API is running'})

        # Basic root route
        @app.route('/')
        def root():
            return jsonify({'message': 'Welcome to HR Assistant Chatbot API'})

        print("✅ Flask app created successfully")
        print("🌐 Starting Flask server on port 5051...")
        app.run(host="0.0.0.0", port=5051, debug=False)

    except Exception as e:
        print(f"❌ Error starting server: {e}")
        import traceback
        traceback.print_exc()
        raise


if __name__ == "__main__":
    main()