"""
Simplified main application entry point for testing fixes.
Avoids heavy imports that might cause issues.
"""

import sys
import os

# Add the project root directory to the path
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, project_root)

def create_simple_app():
    """Create a simple Flask app for testing."""
    from flask import Flask, jsonify
    from flask_cors import CORS
    
    app = Flask(__name__)
    CORS(app, origins=["*"], supports_credentials=True)
    
    # Configure app settings
    app.config['SECRET_KEY'] = 'your-secret-key-here'
    app.config['DEBUG'] = False
    
    # Register error handlers
    from src.middleware.error_handler import setup_error_handlers
    setup_error_handlers(app)
    
    # Basic health check route
    @app.route('/health')
    def health_check():
        return {'status': 'healthy', 'message': 'HR Assistant Chatbot is running'}

    # API health check route (for frontend)
    @app.route('/api/health')
    def api_health_check():
        return {'status': 'healthy', 'message': 'HR Assistant Chatbot API is running'}

    # Basic root route
    @app.route('/')
    def root():
        return {'message': 'Welcome to HR Assistant Chatbot API'}
    
    return app


def main():
    """Main application entry point."""
    try:
        print("🚀 Starting simplified server...")

        # Create Flask app
        flask_app = create_simple_app()
        print("✅ Flask app created successfully")

        print("🌐 Starting Flask server on port 5051...")
        print("Debug: About to call flask_app.run()")
        flask_app.run(host="0.0.0.0", port=5051, debug=False)
        print("Debug: flask_app.run() returned")

    except Exception as e:
        print(f"❌ Error starting server: {e}")
        import traceback
        traceback.print_exc()
        raise


if __name__ == "__main__":
    main()
