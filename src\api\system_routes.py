"""
System API routes for the HR Assistant Chatbot.
Provides system health, monitoring, and diagnostic endpoints.
"""

import time
from flask import Blueprint, request, jsonify, g
from src.utils.logger import get_logger
from src.middleware.logging_middleware import log_user_activity, require_logging
from src.services.system_service import SystemService

logger = get_logger(__name__)
system_bp = Blueprint('system', __name__)

# Initialize system service
system_service = SystemService()


@system_bp.route('/system/health', methods=['GET'])
@require_logging('SYSTEM_HEALTH')
def get_system_health():
    """Get comprehensive system health status."""
    try:
        log_user_activity('SYSTEM_HEALTH_REQUEST')
        
        health_data = system_service.get_system_health()
        
        # Return appropriate status code based on health
        status_code = 200
        if health_data.get('overall_status') == 'unhealthy':
            status_code = 503
        elif health_data.get('overall_status') == 'degraded':
            status_code = 200  # Still operational but with issues
        
        return jsonify(health_data), status_code
        
    except Exception as e:
        logger.error(f"Error retrieving system health: {e}", exc_info=True)
        return jsonify({
            'overall_status': 'unhealthy',
            'error': 'Failed to retrieve system health',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown'),
            'timestamp': time.time()
        }), 503


@system_bp.route('/system/performance', methods=['GET'])
@require_logging('SYSTEM_PERFORMANCE')
def get_system_performance():
    """Get system performance metrics."""
    try:
        time_range = request.args.get('time_range', '1h')
        
        log_user_activity('SYSTEM_PERFORMANCE_REQUEST', {
            'time_range': time_range
        })
        
        performance_data = system_service.get_performance_metrics(time_range)
        
        return jsonify(performance_data)
        
    except Exception as e:
        logger.error(f"Error retrieving system performance: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve system performance',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@system_bp.route('/system/services', methods=['GET'])
@require_logging('SYSTEM_SERVICES')
def get_service_status():
    """Get status of all system services."""
    try:
        log_user_activity('SYSTEM_SERVICES_REQUEST')
        
        services_data = system_service.get_service_status()
        
        return jsonify(services_data)
        
    except Exception as e:
        logger.error(f"Error retrieving service status: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve service status',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@system_bp.route('/system/uptime', methods=['GET'])
@require_logging('SYSTEM_UPTIME')
def get_system_uptime():
    """Get system uptime information."""
    try:
        log_user_activity('SYSTEM_UPTIME_REQUEST')
        
        uptime_data = system_service.get_uptime_info()
        
        return jsonify(uptime_data)
        
    except Exception as e:
        logger.error(f"Error retrieving system uptime: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve system uptime',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@system_bp.route('/system/errors', methods=['GET'])
@require_logging('SYSTEM_ERRORS')
def get_error_logs():
    """Get system error logs."""
    try:
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        severity = request.args.get('severity')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        if limit > 100:
            limit = 100
        
        log_user_activity('SYSTEM_ERRORS_REQUEST', {
            'limit': limit,
            'offset': offset,
            'severity': severity,
            'start_date': start_date,
            'end_date': end_date
        })
        
        error_logs = system_service.get_error_logs(
            limit=limit,
            offset=offset,
            severity=severity,
            start_date=start_date,
            end_date=end_date
        )
        
        return jsonify(error_logs)
        
    except Exception as e:
        logger.error(f"Error retrieving error logs: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve error logs',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@system_bp.route('/system/slow-queries', methods=['GET'])
@require_logging('SYSTEM_SLOW_QUERIES')
def get_slow_queries():
    """Get slow query logs and performance issues."""
    try:
        limit = request.args.get('limit', 20, type=int)
        threshold = request.args.get('threshold', 5.0, type=float)  # seconds
        
        log_user_activity('SYSTEM_SLOW_QUERIES_REQUEST', {
            'limit': limit,
            'threshold': threshold
        })
        
        slow_queries = system_service.get_slow_queries(limit, threshold)
        
        return jsonify(slow_queries)
        
    except Exception as e:
        logger.error(f"Error retrieving slow queries: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve slow queries',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@system_bp.route('/health/documents', methods=['GET'])
@require_logging('DOCUMENT_HEALTH')
def get_document_health():
    """Get document processing and vector store health."""
    try:
        log_user_activity('DOCUMENT_HEALTH_REQUEST')
        
        document_health = system_service.get_document_health()
        
        return jsonify(document_health)
        
    except Exception as e:
        logger.error(f"Error retrieving document health: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve document health',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@system_bp.route('/system/metrics', methods=['GET'])
@require_logging('SYSTEM_METRICS')
def get_system_metrics():
    """Get comprehensive system metrics."""
    try:
        metric_type = request.args.get('type', 'all')
        time_range = request.args.get('time_range', '1h')
        
        log_user_activity('SYSTEM_METRICS_REQUEST', {
            'metric_type': metric_type,
            'time_range': time_range
        })
        
        metrics_data = system_service.get_system_metrics(metric_type, time_range)
        
        return jsonify(metrics_data)
        
    except Exception as e:
        logger.error(f"Error retrieving system metrics: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve system metrics',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@system_bp.route('/system/diagnostics', methods=['GET'])
@require_logging('SYSTEM_DIAGNOSTICS')
def run_system_diagnostics():
    """Run comprehensive system diagnostics."""
    try:
        log_user_activity('SYSTEM_DIAGNOSTICS_REQUEST')
        
        diagnostics_data = system_service.run_diagnostics()
        
        return jsonify(diagnostics_data)
        
    except Exception as e:
        logger.error(f"Error running system diagnostics: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to run system diagnostics',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@system_bp.route('/system/restart', methods=['POST'])
@require_logging('SYSTEM_RESTART')
def restart_system():
    """Restart system services (admin only)."""
    try:
        data = request.get_json() or {}
        service_name = data.get('service', 'all')
        
        log_user_activity('SYSTEM_RESTART_REQUEST', {
            'service': service_name
        })
        
        # This would require proper authentication and authorization
        restart_result = system_service.restart_service(service_name)
        
        return jsonify(restart_result)
        
    except Exception as e:
        logger.error(f"Error restarting system: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to restart system',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@system_bp.route('/system/backup', methods=['POST'])
@require_logging('SYSTEM_BACKUP')
def create_system_backup():
    """Create system backup (admin only)."""
    try:
        data = request.get_json() or {}
        backup_type = data.get('type', 'full')
        
        log_user_activity('SYSTEM_BACKUP_REQUEST', {
            'backup_type': backup_type
        })
        
        backup_result = system_service.create_backup(backup_type)
        
        return jsonify(backup_result)
        
    except Exception as e:
        logger.error(f"Error creating system backup: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to create system backup',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500
