"""
System service for monitoring, diagnostics, and system management.
"""

import time
import os
import psutil
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from src.utils.logger import get_logger
from src.database.conversation_store import ConversationStore
from src.database.vector_store import QdrantVectorStore

logger = get_logger(__name__)


class SystemService:
    """Service for system monitoring and management."""
    
    def __init__(self):
        """Initialize the system service."""
        self.start_time = time.time()
        self.conversation_store = ConversationStore()
        logger.info("SystemService initialized successfully")
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get comprehensive system health status."""
        try:
            health_data = {
                'overall_status': 'healthy',
                'timestamp': time.time(),
                'components': {
                    'database': self._check_database_health(),
                    'vector_store': self._check_vector_store_health(),
                    'chat_service': self._check_chat_service_health(),
                    'file_system': self._check_file_system_health(),
                    'memory': self._check_memory_health(),
                    'disk': self._check_disk_health()
                },
                'metrics': {
                    'uptime_seconds': time.time() - self.start_time,
                    'cpu_usage': psutil.cpu_percent(interval=1),
                    'memory_usage': psutil.virtual_memory().percent,
                    'disk_usage': self._get_disk_usage_percent()
                }
            }
            
            # Determine overall status
            component_statuses = [comp.get('status', 'unknown') for comp in health_data['components'].values()]
            if 'unhealthy' in component_statuses:
                health_data['overall_status'] = 'unhealthy'
            elif 'degraded' in component_statuses:
                health_data['overall_status'] = 'degraded'
            
            return health_data
            
        except Exception as e:
            logger.error(f"Error getting system health: {e}", exc_info=True)
            return {
                'overall_status': 'unhealthy',
                'error': str(e),
                'timestamp': time.time()
            }
    
    def get_performance_metrics(self, time_range: str = '1h') -> Dict[str, Any]:
        """Get system performance metrics."""
        try:
            # Get CPU and memory metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = self._get_disk_usage()
            
            # Get network stats
            try:
                network = psutil.net_io_counters()
            except:
                network = None

            # Get process information
            try:
                process = psutil.Process()
                process_memory = process.memory_info()
            except:
                process_memory = None

            return {
                'cpu': {
                    'usage_percent': cpu_percent,
                    'count': psutil.cpu_count(),
                    'load_average': None  # Skip load_average on Windows
                },
                'memory': {
                    'total_gb': round(memory.total / (1024**3), 2),
                    'available_gb': round(memory.available / (1024**3), 2),
                    'used_gb': round(memory.used / (1024**3), 2),
                    'usage_percent': memory.percent,
                    'process_memory_mb': round(process_memory.rss / (1024**2), 2) if process_memory else 0
                },
                'disk': {
                    'total_gb': round(disk.total / (1024**3), 2),
                    'free_gb': round(disk.free / (1024**3), 2),
                    'used_gb': round(disk.used / (1024**3), 2),
                    'usage_percent': round((disk.used / disk.total) * 100, 2)
                } if disk else {'status': 'unavailable'},
                'network': {
                    'bytes_sent': network.bytes_sent if network else 0,
                    'bytes_recv': network.bytes_recv if network else 0,
                    'packets_sent': network.packets_sent if network else 0,
                    'packets_recv': network.packets_recv if network else 0
                } if network else {'status': 'unavailable'},
                'uptime_seconds': time.time() - self.start_time,
                'timestamp': time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}", exc_info=True)
            return {'error': str(e), 'timestamp': time.time()}
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get status of all system services."""
        try:
            services = {
                'chat_service': self._check_chat_service_health(),
                'vector_store': self._check_vector_store_health(),
                'database': self._check_database_health(),
                'analytics': self._check_analytics_health(),
                'embedding_generator': self._check_embedding_health(),
                'intent_classifier': self._check_intent_classifier_health()
            }
            
            # Count service statuses
            healthy_count = sum(1 for s in services.values() if s.get('status') == 'healthy')
            total_count = len(services)
            
            return {
                'services': services,
                'summary': {
                    'total_services': total_count,
                    'healthy_services': healthy_count,
                    'unhealthy_services': total_count - healthy_count,
                    'overall_health': 'healthy' if healthy_count == total_count else 'degraded'
                },
                'timestamp': time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting service status: {e}", exc_info=True)
            return {'error': str(e), 'timestamp': time.time()}
    
    def get_uptime_info(self) -> Dict[str, Any]:
        """Get system uptime information."""
        try:
            uptime_seconds = time.time() - self.start_time
            uptime_days = int(uptime_seconds // 86400)
            uptime_hours = int((uptime_seconds % 86400) // 3600)
            uptime_minutes = int((uptime_seconds % 3600) // 60)
            
            return {
                'uptime_seconds': uptime_seconds,
                'uptime_formatted': f"{uptime_days}d {uptime_hours}h {uptime_minutes}m",
                'start_time': self.start_time,
                'start_time_iso': datetime.fromtimestamp(self.start_time).isoformat(),
                'current_time': time.time(),
                'current_time_iso': datetime.now().isoformat(),
                'timestamp': time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting uptime info: {e}", exc_info=True)
            return {'error': str(e), 'timestamp': time.time()}
    
    def get_error_logs(self, limit: int = 50, offset: int = 0, severity: Optional[str] = None,
                      start_date: Optional[str] = None, end_date: Optional[str] = None) -> Dict[str, Any]:
        """Get system error logs."""
        try:
            # This would read from actual log files
            # For now, return mock error data
            mock_errors = [
                {
                    'id': 1,
                    'timestamp': '2025-08-10T10:30:00Z',
                    'severity': 'ERROR',
                    'component': 'vector_search',
                    'message': 'Timeout connecting to Qdrant',
                    'details': 'Connection timeout after 30 seconds'
                },
                {
                    'id': 2,
                    'timestamp': '2025-08-10T10:25:00Z',
                    'severity': 'WARNING',
                    'component': 'chat_service',
                    'message': 'Slow response time detected',
                    'details': 'Response took 8.5 seconds'
                },
                {
                    'id': 3,
                    'timestamp': '2025-08-10T10:20:00Z',
                    'severity': 'ERROR',
                    'component': 'embedding_generator',
                    'message': 'Model loading failed',
                    'details': 'CUDA out of memory'
                }
            ]
            
            # Filter by severity if specified
            if severity:
                mock_errors = [err for err in mock_errors if err['severity'] == severity]
            
            return {
                'errors': mock_errors[offset:offset+limit],
                'total': len(mock_errors),
                'limit': limit,
                'offset': offset,
                'filters': {
                    'severity': severity,
                    'start_date': start_date,
                    'end_date': end_date
                },
                'timestamp': time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting error logs: {e}", exc_info=True)
            return {'error': str(e), 'timestamp': time.time()}
    
    def get_slow_queries(self, limit: int = 20, threshold: float = 5.0) -> Dict[str, Any]:
        """Get slow query logs."""
        try:
            # Get recent conversations with slow response times
            conversations = self.conversation_store.get_all_conversations(limit=100)
            
            slow_queries = []
            for conv in conversations:
                response_time = conv.get('response_time_seconds', 0)
                if response_time > threshold:
                    slow_queries.append({
                        'query_id': conv.get('id'),
                        'device_id': conv.get('device_id'),
                        'query_preview': conv.get('user_query', '')[:100] + '...',
                        'response_time': response_time,
                        'timestamp': conv.get('query_timestamp'),
                        'intent': conv.get('intent')
                    })
            
            # Sort by response time descending
            slow_queries.sort(key=lambda x: x['response_time'], reverse=True)
            
            return {
                'slow_queries': slow_queries[:limit],
                'total': len(slow_queries),
                'threshold_seconds': threshold,
                'avg_slow_time': sum(q['response_time'] for q in slow_queries) / len(slow_queries) if slow_queries else 0,
                'timestamp': time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting slow queries: {e}", exc_info=True)
            return {'error': str(e), 'timestamp': time.time()}
    
    def get_document_health(self) -> Dict[str, Any]:
        """Get document processing and vector store health."""
        try:
            # Check vector store
            vector_health = self._check_vector_store_health()
            
            # Check document directories
            from src.config import DATA_DIR, RAW_DIR, PROCESSED_DIR
            
            document_stats = {
                'raw_documents': len(list(RAW_DIR.glob('*'))) if RAW_DIR.exists() else 0,
                'processed_documents': len(list(PROCESSED_DIR.glob('*'))) if PROCESSED_DIR.exists() else 0,
                'data_directory_size': self._get_directory_size(DATA_DIR) if DATA_DIR.exists() else 0
            }
            
            return {
                'vector_store': vector_health,
                'document_stats': document_stats,
                'storage_health': {
                    'data_dir_exists': DATA_DIR.exists(),
                    'raw_dir_exists': RAW_DIR.exists(),
                    'processed_dir_exists': PROCESSED_DIR.exists()
                },
                'timestamp': time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting document health: {e}", exc_info=True)
            return {'error': str(e), 'timestamp': time.time()}

    def get_system_metrics(self, metric_type: str = 'all', time_range: str = '1h') -> Dict[str, Any]:
        """Get comprehensive system metrics."""
        try:
            metrics = {}

            if metric_type in ['all', 'performance']:
                metrics['performance'] = self.get_performance_metrics(time_range)

            if metric_type in ['all', 'chat']:
                metrics['chat'] = self._get_chat_metrics(time_range)

            if metric_type in ['all', 'errors']:
                metrics['errors'] = self._get_error_metrics(time_range)

            if metric_type in ['all', 'usage']:
                metrics['usage'] = self._get_usage_metrics(time_range)

            return {
                'metrics': metrics,
                'metric_type': metric_type,
                'time_range': time_range,
                'timestamp': time.time()
            }

        except Exception as e:
            logger.error(f"Error getting system metrics: {e}", exc_info=True)
            return {'error': str(e), 'timestamp': time.time()}

    def run_diagnostics(self) -> Dict[str, Any]:
        """Run comprehensive system diagnostics."""
        try:
            diagnostics = {
                'system_health': self.get_system_health(),
                'performance': self.get_performance_metrics(),
                'services': self.get_service_status(),
                'document_health': self.get_document_health(),
                'connectivity_tests': self._run_connectivity_tests(),
                'resource_checks': self._run_resource_checks(),
                'timestamp': time.time()
            }

            # Determine overall diagnostic result
            issues = []
            if diagnostics['system_health'].get('overall_status') != 'healthy':
                issues.append('System health issues detected')

            if diagnostics['services']['summary']['unhealthy_services'] > 0:
                issues.append(f"{diagnostics['services']['summary']['unhealthy_services']} services unhealthy")

            diagnostics['summary'] = {
                'overall_status': 'healthy' if not issues else 'issues_detected',
                'issues_found': len(issues),
                'issues': issues
            }

            return diagnostics

        except Exception as e:
            logger.error(f"Error running diagnostics: {e}", exc_info=True)
            return {'error': str(e), 'timestamp': time.time()}

    def restart_service(self, service_name: str) -> Dict[str, Any]:
        """Restart a specific service."""
        try:
            # This would implement actual service restart logic
            # For now, return mock response
            return {
                'success': True,
                'service': service_name,
                'message': f'Service {service_name} restart initiated',
                'timestamp': time.time()
            }

        except Exception as e:
            logger.error(f"Error restarting service {service_name}: {e}", exc_info=True)
            return {'success': False, 'error': str(e), 'timestamp': time.time()}

    def create_backup(self, backup_type: str) -> Dict[str, Any]:
        """Create system backup."""
        try:
            # This would implement actual backup logic
            backup_id = f"backup_{int(time.time())}"

            return {
                'success': True,
                'backup_id': backup_id,
                'backup_type': backup_type,
                'message': f'Backup {backup_id} created successfully',
                'timestamp': time.time()
            }

        except Exception as e:
            logger.error(f"Error creating backup: {e}", exc_info=True)
            return {'success': False, 'error': str(e), 'timestamp': time.time()}

    # Health check methods
    def _check_database_health(self) -> Dict[str, Any]:
        """Check database health."""
        try:
            # Test conversation store
            self.conversation_store.get_all_conversations(limit=1)
            return {'status': 'healthy', 'message': 'Database connection successful'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': f'Database error: {str(e)}'}

    def _check_vector_store_health(self) -> Dict[str, Any]:
        """Check vector store health."""
        try:
            vector_store = QdrantVectorStore()
            info = vector_store.get_collection_info()
            return {
                'status': 'healthy',
                'message': 'Vector store operational',
                'points_count': info.get('points_count', 0)
            }
        except Exception as e:
            return {'status': 'unhealthy', 'message': f'Vector store error: {str(e)}'}

    def _check_chat_service_health(self) -> Dict[str, Any]:
        """Check chat service health."""
        try:
            # This would test chat service components
            return {'status': 'healthy', 'message': 'Chat service operational'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': f'Chat service error: {str(e)}'}

    def _check_file_system_health(self) -> Dict[str, Any]:
        """Check file system health."""
        try:
            from src.config import DATA_DIR
            if not DATA_DIR.exists():
                return {'status': 'unhealthy', 'message': 'Data directory does not exist'}

            # Check disk space
            disk = self._get_disk_usage()
            if not disk:
                return {'status': 'degraded', 'message': 'Disk usage unavailable'}
            free_percent = (disk.free / disk.total) * 100

            if free_percent < 10:
                return {'status': 'degraded', 'message': f'Low disk space: {free_percent:.1f}% free'}
            elif free_percent < 5:
                return {'status': 'unhealthy', 'message': f'Critical disk space: {free_percent:.1f}% free'}

            return {'status': 'healthy', 'message': f'File system healthy: {free_percent:.1f}% free'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': f'File system error: {str(e)}'}

    def _check_memory_health(self) -> Dict[str, Any]:
        """Check memory health."""
        try:
            memory = psutil.virtual_memory()
            if memory.percent > 90:
                return {'status': 'unhealthy', 'message': f'Critical memory usage: {memory.percent}%'}
            elif memory.percent > 80:
                return {'status': 'degraded', 'message': f'High memory usage: {memory.percent}%'}

            return {'status': 'healthy', 'message': f'Memory usage normal: {memory.percent}%'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': f'Memory check error: {str(e)}'}

    def _check_disk_health(self) -> Dict[str, Any]:
        """Check disk health."""
        try:
            disk = self._get_disk_usage()
            if not disk:
                return {'status': 'degraded', 'message': 'Disk usage unavailable'}

            usage_percent = (disk.used / disk.total) * 100

            if usage_percent > 95:
                return {'status': 'unhealthy', 'message': f'Critical disk usage: {usage_percent:.1f}%'}
            elif usage_percent > 85:
                return {'status': 'degraded', 'message': f'High disk usage: {usage_percent:.1f}%'}

            return {'status': 'healthy', 'message': f'Disk usage normal: {usage_percent:.1f}%'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': f'Disk check error: {str(e)}'}

    def _check_analytics_health(self) -> Dict[str, Any]:
        """Check analytics service health."""
        try:
            return {'status': 'healthy', 'message': 'Analytics service operational'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': f'Analytics error: {str(e)}'}

    def _check_embedding_health(self) -> Dict[str, Any]:
        """Check embedding generator health."""
        try:
            return {'status': 'healthy', 'message': 'Embedding generator operational'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': f'Embedding error: {str(e)}'}

    def _check_intent_classifier_health(self) -> Dict[str, Any]:
        """Check intent classifier health."""
        try:
            return {'status': 'healthy', 'message': 'Intent classifier operational'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': f'Intent classifier error: {str(e)}'}

    def _get_directory_size(self, path) -> int:
        """Get directory size in bytes."""
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
        return total_size

    def _get_chat_metrics(self, time_range: str) -> Dict[str, Any]:
        """Get chat-specific metrics."""
        try:
            conversations = self.conversation_store.get_all_conversations(limit=100)
            return {
                'total_conversations': len(conversations),
                'avg_response_time': sum(c.get('response_time_seconds', 0) for c in conversations) / len(conversations) if conversations else 0,
                'unique_users': len(set(c['device_id'] for c in conversations))
            }
        except Exception as e:
            return {'error': str(e)}

    def _get_error_metrics(self, time_range: str) -> Dict[str, Any]:
        """Get error metrics."""
        return {
            'total_errors': 5,
            'error_rate': 0.02,
            'critical_errors': 1
        }

    def _get_usage_metrics(self, time_range: str) -> Dict[str, Any]:
        """Get usage metrics."""
        return {
            'api_calls': 1250,
            'active_users': 45,
            'peak_concurrent_users': 12
        }

    def _run_connectivity_tests(self) -> Dict[str, Any]:
        """Run connectivity tests."""
        return {
            'vector_store': {'status': 'connected', 'latency_ms': 45},
            'database': {'status': 'connected', 'latency_ms': 12},
            'external_apis': {'status': 'connected', 'latency_ms': 120}
        }

    def _run_resource_checks(self) -> Dict[str, Any]:
        """Run resource availability checks."""
        return {
            'cpu_available': True,
            'memory_available': True,
            'disk_space_available': True,
            'network_available': True
        }

    def _get_disk_usage_percent(self) -> float:
        """Get disk usage percentage safely."""
        try:
            import os
            if os.name == 'nt':  # Windows
                disk = psutil.disk_usage('C:')
            else:  # Unix/Linux
                disk = psutil.disk_usage('/')
            return round((disk.used / disk.total) * 100, 2)
        except Exception as e:
            logger.warning(f"Could not get disk usage: {e}")
            return 0.0

    def _get_disk_usage(self):
        """Get disk usage safely."""
        try:
            import os
            if os.name == 'nt':  # Windows
                return psutil.disk_usage('C:')
            else:  # Unix/Linux
                return psutil.disk_usage('/')
        except Exception as e:
            logger.warning(f"Could not get disk usage: {e}")
            return None
