"""
Analytics service for generating real-time metrics and insights.
Aggregates data from conversations, logs, and system metrics.
"""

import time
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from collections import defaultdict, Counter
from src.utils.logger import get_logger
from src.database.conversation_store import ConversationStore
from src.config import CONVERSATION_DB_PATH

logger = get_logger(__name__)


class AnalyticsService:
    """Service for generating analytics and metrics from chat data."""
    
    def __init__(self):
        """Initialize the analytics service."""
        self.conversation_store = ConversationStore()
        logger.info("AnalyticsService initialized successfully")
    
    def _parse_time_range(self, time_range: str) -> datetime:
        """Parse time range string and return start datetime."""
        now = datetime.now()
        
        if time_range == '1h':
            return now - timedelta(hours=1)
        elif time_range == '24h':
            return now - timedelta(hours=24)
        elif time_range == '7d':
            return now - timedelta(days=7)
        elif time_range == '30d':
            return now - timedelta(days=30)
        elif time_range == '90d':
            return now - timedelta(days=90)
        else:
            return now - timedelta(hours=24)  # Default to 24h
    
    def get_live_analytics(self, time_range: str = '24h') -> Dict[str, Any]:
        """Get real-time analytics dashboard data."""
        try:
            start_time = self._parse_time_range(time_range)
            
            # Get conversations from the specified time range
            conversations = self.conversation_store.get_all_conversations(
                start_date=start_time.isoformat(),
                limit=1000
            )
            
            # Calculate basic metrics
            total_conversations = len(conversations)
            unique_users = len(set(conv['device_id'] for conv in conversations))
            
            # Calculate average response time
            response_times = [conv.get('response_time_seconds', 0) for conv in conversations if conv.get('response_time_seconds')]
            avg_response_time = sum(response_times) / len(response_times) if response_times else 0
            
            # Intent distribution
            intents = [conv.get('intent') for conv in conversations if conv.get('intent')]
            intent_counts = Counter(intents)
            
            # Language distribution
            languages = [conv.get('language', 'en') for conv in conversations]
            language_counts = Counter(languages)
            
            # Hourly activity (last 24 hours)
            hourly_activity = defaultdict(int)
            for conv in conversations:
                try:
                    timestamp = datetime.fromisoformat(conv['query_timestamp'])
                    hour_key = timestamp.strftime('%H:00')
                    hourly_activity[hour_key] += 1
                except:
                    continue
            
            return {
                'timestamp': time.time(),
                'time_range': time_range,
                'summary': {
                    'total_conversations': total_conversations,
                    'unique_users': unique_users,
                    'avg_response_time': round(avg_response_time, 2),
                    'conversations_per_user': round(total_conversations / unique_users, 2) if unique_users > 0 else 0
                },
                'intents': {
                    'total': len(intent_counts),
                    'distribution': dict(intent_counts.most_common(10))
                },
                'languages': {
                    'total': len(language_counts),
                    'distribution': dict(language_counts)
                },
                'activity': {
                    'hourly': dict(hourly_activity),
                    'total_hours': len(hourly_activity)
                },
                'performance': {
                    'avg_response_time': round(avg_response_time, 2),
                    'fast_responses': len([t for t in response_times if t < 2.0]),
                    'slow_responses': len([t for t in response_times if t > 5.0])
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating live analytics: {e}", exc_info=True)
            return {
                'error': str(e),
                'timestamp': time.time(),
                'time_range': time_range
            }
    
    def get_metrics(self, metric_name: str, time_range: str = '24h') -> Optional[Dict[str, Any]]:
        """Get specific metrics by name."""
        try:
            start_time = self._parse_time_range(time_range)
            
            if metric_name == 'chatbot':
                return self._get_chatbot_metrics(start_time)
            elif metric_name == 'performance':
                return self._get_performance_metrics(start_time)
            elif metric_name == 'engagement':
                return self._get_engagement_metrics(start_time)
            elif metric_name == 'sentiment':
                return self._get_sentiment_metrics(start_time)
            elif metric_name == 'intents':
                return self._get_intent_metrics(start_time)
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error getting metrics {metric_name}: {e}", exc_info=True)
            return {'error': str(e), 'metric': metric_name}
    
    def _get_chatbot_metrics(self, start_time: datetime) -> Dict[str, Any]:
        """Get chatbot-specific metrics."""
        conversations = self.conversation_store.get_all_conversations(
            start_date=start_time.isoformat(),
            limit=1000
        )
        
        return {
            'total_queries': len(conversations),
            'unique_users': len(set(conv['device_id'] for conv in conversations)),
            'success_rate': 100.0,  # Placeholder - would need error tracking
            'avg_query_length': sum(len(conv['user_query']) for conv in conversations) / len(conversations) if conversations else 0,
            'timestamp': time.time()
        }
    
    def _get_performance_metrics(self, start_time: datetime) -> Dict[str, Any]:
        """Get performance metrics."""
        conversations = self.conversation_store.get_all_conversations(
            start_date=start_time.isoformat(),
            limit=1000
        )
        
        response_times = [conv.get('response_time_seconds', 0) for conv in conversations if conv.get('response_time_seconds')]
        
        return {
            'avg_response_time': sum(response_times) / len(response_times) if response_times else 0,
            'min_response_time': min(response_times) if response_times else 0,
            'max_response_time': max(response_times) if response_times else 0,
            'fast_responses_count': len([t for t in response_times if t < 2.0]),
            'slow_responses_count': len([t for t in response_times if t > 5.0]),
            'timestamp': time.time()
        }
    
    def _get_engagement_metrics(self, start_time: datetime) -> Dict[str, Any]:
        """Get user engagement metrics."""
        conversations = self.conversation_store.get_all_conversations(
            start_date=start_time.isoformat(),
            limit=1000
        )
        
        user_activity = defaultdict(int)
        for conv in conversations:
            user_activity[conv['device_id']] += 1
        
        return {
            'total_users': len(user_activity),
            'total_interactions': len(conversations),
            'avg_interactions_per_user': sum(user_activity.values()) / len(user_activity) if user_activity else 0,
            'active_users': len([u for u, count in user_activity.items() if count > 1]),
            'timestamp': time.time()
        }
    
    def _get_sentiment_metrics(self, start_time: datetime) -> Dict[str, Any]:
        """Get sentiment analysis metrics (placeholder)."""
        # This would integrate with actual sentiment analysis
        return {
            'positive': 65,
            'neutral': 25,
            'negative': 10,
            'total_analyzed': 100,
            'timestamp': time.time()
        }
    
    def _get_intent_metrics(self, start_time: datetime) -> Dict[str, Any]:
        """Get intent classification metrics."""
        conversations = self.conversation_store.get_all_conversations(
            start_date=start_time.isoformat(),
            limit=1000
        )
        
        intents = [conv.get('intent') for conv in conversations if conv.get('intent')]
        intent_counts = Counter(intents)
        
        return {
            'total_classified': len(intents),
            'unique_intents': len(intent_counts),
            'top_intents': dict(intent_counts.most_common(10)),
            'classification_rate': len(intents) / len(conversations) * 100 if conversations else 0,
            'timestamp': time.time()
        }
    
    def get_user_metrics(self, time_range: str = '7d') -> Dict[str, Any]:
        """Get comprehensive user metrics."""
        try:
            start_time = self._parse_time_range(time_range)
            conversations = self.conversation_store.get_all_conversations(
                start_date=start_time.isoformat(),
                limit=1000
            )
            
            # User activity analysis
            user_activity = defaultdict(list)
            for conv in conversations:
                user_activity[conv['device_id']].append(conv)
            
            # Intent analysis
            intents = [conv.get('intent') for conv in conversations if conv.get('intent')]
            intent_counts = Counter(intents)
            
            # Usage patterns
            usage_by_day = defaultdict(int)
            for conv in conversations:
                try:
                    timestamp = datetime.fromisoformat(conv['query_timestamp'])
                    day_key = timestamp.strftime('%Y-%m-%d')
                    usage_by_day[day_key] += 1
                except:
                    continue
            
            return {
                'total_users': len(user_activity),
                'total_conversations': len(conversations),
                'usage': [{'date': date, 'count': count} for date, count in sorted(usage_by_day.items())],
                'intents': [{'intent': intent, 'count': count} for intent, count in intent_counts.most_common(10)],
                'user_engagement': {
                    'avg_conversations_per_user': len(conversations) / len(user_activity) if user_activity else 0,
                    'active_users': len([u for u, convs in user_activity.items() if len(convs) > 1]),
                    'power_users': len([u for u, convs in user_activity.items() if len(convs) > 5])
                },
                'timestamp': time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting user metrics: {e}", exc_info=True)
            return {'error': str(e), 'timestamp': time.time()}
    
    def get_chatlogs(self, limit: int = 50, offset: int = 0, 
                    start_date: Optional[str] = None, end_date: Optional[str] = None,
                    device_id: Optional[str] = None, intent: Optional[str] = None) -> Dict[str, Any]:
        """Get filtered chat logs."""
        try:
            conversations = self.conversation_store.get_all_conversations(
                device_id=device_id,
                start_date=start_date,
                end_date=end_date,
                limit=limit,
                offset=offset
            )
            
            # Filter by intent if specified
            if intent:
                conversations = [conv for conv in conversations if conv.get('intent') == intent]
            
            return {
                'chatlogs': conversations,
                'total': len(conversations),
                'limit': limit,
                'offset': offset,
                'filters': {
                    'start_date': start_date,
                    'end_date': end_date,
                    'device_id': device_id,
                    'intent': intent
                },
                'timestamp': time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting chatlogs: {e}", exc_info=True)
            return {'error': str(e), 'timestamp': time.time()}

    def get_chat_trends(self, time_range: str = '7d') -> Dict[str, Any]:
        """Get chat trends and patterns over time."""
        try:
            start_time = self._parse_time_range(time_range)
            conversations = self.conversation_store.get_all_conversations(
                start_date=start_time.isoformat(),
                limit=1000
            )

            # Daily trends
            daily_trends = defaultdict(int)
            hourly_trends = defaultdict(int)

            for conv in conversations:
                try:
                    timestamp = datetime.fromisoformat(conv['query_timestamp'])
                    day_key = timestamp.strftime('%Y-%m-%d')
                    hour_key = timestamp.strftime('%H:00')
                    daily_trends[day_key] += 1
                    hourly_trends[hour_key] += 1
                except:
                    continue

            return {
                'daily_trends': [{'date': date, 'count': count} for date, count in sorted(daily_trends.items())],
                'hourly_trends': [{'hour': hour, 'count': count} for hour, count in sorted(hourly_trends.items())],
                'peak_hour': max(hourly_trends.items(), key=lambda x: x[1])[0] if hourly_trends else None,
                'peak_day': max(daily_trends.items(), key=lambda x: x[1])[0] if daily_trends else None,
                'total_conversations': len(conversations),
                'timestamp': time.time()
            }

        except Exception as e:
            logger.error(f"Error getting chat trends: {e}", exc_info=True)
            return {'error': str(e), 'timestamp': time.time()}

    def get_intent_analytics(self, time_range: str = '7d') -> Dict[str, Any]:
        """Get detailed intent classification analytics."""
        try:
            start_time = self._parse_time_range(time_range)
            conversations = self.conversation_store.get_all_conversations(
                start_date=start_time.isoformat(),
                limit=1000
            )

            intents = [conv.get('intent') for conv in conversations if conv.get('intent')]
            intent_counts = Counter(intents)

            # Intent trends over time
            intent_trends = defaultdict(lambda: defaultdict(int))
            for conv in conversations:
                if conv.get('intent'):
                    try:
                        timestamp = datetime.fromisoformat(conv['query_timestamp'])
                        day_key = timestamp.strftime('%Y-%m-%d')
                        intent_trends[conv['intent']][day_key] += 1
                    except:
                        continue

            return {
                'total_classified': len(intents),
                'classification_rate': len(intents) / len(conversations) * 100 if conversations else 0,
                'intent_distribution': dict(intent_counts.most_common()),
                'top_intents': dict(intent_counts.most_common(10)),
                'intent_trends': {intent: dict(trends) for intent, trends in intent_trends.items()},
                'unique_intents': len(intent_counts),
                'timestamp': time.time()
            }

        except Exception as e:
            logger.error(f"Error getting intent analytics: {e}", exc_info=True)
            return {'error': str(e), 'timestamp': time.time()}

    def get_performance_metrics(self, time_range: str = '24h') -> Dict[str, Any]:
        """Get detailed system performance metrics."""
        try:
            start_time = self._parse_time_range(time_range)
            conversations = self.conversation_store.get_all_conversations(
                start_date=start_time.isoformat(),
                limit=1000
            )

            response_times = [conv.get('response_time_seconds', 0) for conv in conversations if conv.get('response_time_seconds')]

            # Performance buckets
            fast_responses = [t for t in response_times if t < 1.0]
            normal_responses = [t for t in response_times if 1.0 <= t < 3.0]
            slow_responses = [t for t in response_times if 3.0 <= t < 10.0]
            very_slow_responses = [t for t in response_times if t >= 10.0]

            return {
                'total_requests': len(conversations),
                'avg_response_time': sum(response_times) / len(response_times) if response_times else 0,
                'median_response_time': sorted(response_times)[len(response_times)//2] if response_times else 0,
                'min_response_time': min(response_times) if response_times else 0,
                'max_response_time': max(response_times) if response_times else 0,
                'performance_buckets': {
                    'fast': {'count': len(fast_responses), 'percentage': len(fast_responses) / len(response_times) * 100 if response_times else 0},
                    'normal': {'count': len(normal_responses), 'percentage': len(normal_responses) / len(response_times) * 100 if response_times else 0},
                    'slow': {'count': len(slow_responses), 'percentage': len(slow_responses) / len(response_times) * 100 if response_times else 0},
                    'very_slow': {'count': len(very_slow_responses), 'percentage': len(very_slow_responses) / len(response_times) * 100 if response_times else 0}
                },
                'timestamp': time.time()
            }

        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}", exc_info=True)
            return {'error': str(e), 'timestamp': time.time()}

    def get_sentiment_analysis(self, time_range: str = '7d') -> Dict[str, Any]:
        """Get sentiment analysis of conversations (placeholder implementation)."""
        try:
            # This would integrate with actual sentiment analysis
            # For now, return mock data based on conversation patterns
            start_time = self._parse_time_range(time_range)
            conversations = self.conversation_store.get_all_conversations(
                start_date=start_time.isoformat(),
                limit=1000
            )

            total_conversations = len(conversations)

            # Mock sentiment distribution
            return {
                'total_analyzed': total_conversations,
                'sentiment_distribution': {
                    'positive': {'count': int(total_conversations * 0.65), 'percentage': 65.0},
                    'neutral': {'count': int(total_conversations * 0.25), 'percentage': 25.0},
                    'negative': {'count': int(total_conversations * 0.10), 'percentage': 10.0}
                },
                'sentiment_trends': {
                    # Mock daily sentiment trends
                    '2025-08-10': {'positive': 20, 'neutral': 8, 'negative': 2},
                    '2025-08-09': {'positive': 18, 'neutral': 7, 'negative': 3},
                    '2025-08-08': {'positive': 22, 'neutral': 6, 'negative': 1}
                },
                'timestamp': time.time()
            }

        except Exception as e:
            logger.error(f"Error getting sentiment analysis: {e}", exc_info=True)
            return {'error': str(e), 'timestamp': time.time()}

    def get_retrieval_analytics(self, time_range: str = '24h') -> Dict[str, Any]:
        """Get vector retrieval performance analytics."""
        try:
            # This would analyze retrieval logs for performance metrics
            # For now, return mock data
            return {
                'total_retrievals': 150,
                'avg_retrieval_time': 0.85,
                'successful_retrievals': 145,
                'failed_retrievals': 5,
                'success_rate': 96.7,
                'avg_results_per_query': 8.2,
                'retrieval_performance': {
                    'fast': {'count': 120, 'percentage': 80.0},
                    'normal': {'count': 25, 'percentage': 16.7},
                    'slow': {'count': 5, 'percentage': 3.3}
                },
                'timestamp': time.time()
            }

        except Exception as e:
            logger.error(f"Error getting retrieval analytics: {e}", exc_info=True)
            return {'error': str(e), 'timestamp': time.time()}
