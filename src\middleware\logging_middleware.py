"""
Comprehensive logging middleware for tracking user activities, API calls, and performance.
"""

import time
import json
from flask import request, g, current_app
from functools import wraps
from src.utils.logger import get_logger

logger = get_logger(__name__)


def setup_logging_middleware(app):
    """Setup comprehensive logging middleware for the Flask app."""
    
    @app.before_request
    def before_request():
        """Log incoming requests and start timing."""
        g.start_time = time.time()
        g.request_id = f"{int(time.time() * 1000)}-{id(request)}"
        
        # Log request details
        request_data = {
            'request_id': g.request_id,
            'method': request.method,
            'url': request.url,
            'path': request.path,
            'remote_addr': request.remote_addr,
            'user_agent': request.headers.get('User-Agent', ''),
            'content_type': request.content_type,
            'content_length': request.content_length,
        }
        
        # Log request payload for POST/PUT requests (excluding sensitive data)
        if request.method in ['POST', 'PUT', 'PATCH'] and request.is_json:
            try:
                payload = request.get_json()
                if payload:
                    # Remove sensitive fields
                    safe_payload = {k: v for k, v in payload.items() 
                                  if k.lower() not in ['password', 'token', 'secret', 'key']}
                    request_data['payload_summary'] = {
                        'keys': list(safe_payload.keys()),
                        'query_length': len(safe_payload.get('query', '')),
                        'device_id': safe_payload.get('device_id', ''),
                        'response_mode': safe_payload.get('response_mode', ''),
                    }
            except Exception as e:
                request_data['payload_error'] = str(e)
        
        logger.info("📥 Incoming request", extra=request_data)
    
    @app.after_request
    def after_request(response):
        """Log response details and performance metrics."""
        if hasattr(g, 'start_time'):
            duration = round((time.time() - g.start_time) * 1000, 2)  # milliseconds
            
            response_data = {
                'request_id': getattr(g, 'request_id', 'unknown'),
                'status_code': response.status_code,
                'content_type': response.content_type,
                'content_length': response.content_length,
                'duration_ms': duration,
                'method': request.method,
                'path': request.path,
            }
            
            # Add performance classification
            if duration > 5000:
                response_data['performance'] = 'SLOW'
                logger.warning("🐌 Slow response detected", extra=response_data)
            elif duration > 2000:
                response_data['performance'] = 'MODERATE'
                logger.info("⚡ Response completed", extra=response_data)
            else:
                response_data['performance'] = 'FAST'
                logger.info("⚡ Response completed", extra=response_data)
            
            # Log errors with more detail
            if response.status_code >= 400:
                response_data['error_category'] = 'CLIENT_ERROR' if response.status_code < 500 else 'SERVER_ERROR'
                logger.error("❌ Error response", extra=response_data)
        
        return response


def log_user_activity(activity_type: str, details: dict = None):
    """Log specific user activities with structured data."""
    activity_data = {
        'activity_type': activity_type,
        'timestamp': time.time(),
        'request_id': getattr(g, 'request_id', 'unknown'),
        'user_ip': request.remote_addr if request else 'unknown',
        'user_agent': request.headers.get('User-Agent', '') if request else '',
    }
    
    if details:
        activity_data.update(details)
    
    logger.info(f"👤 User activity: {activity_type}", extra=activity_data)


def log_chat_interaction(query: str, response: str, device_id: str, 
                        retrieval_results: int = 0, intent: str = None, 
                        language: str = None, response_time: float = None):
    """Log chat interactions with detailed context."""
    chat_data = {
        'activity_type': 'CHAT_INTERACTION',
        'device_id': device_id,
        'query_length': len(query),
        'query_preview': query[:100] + '...' if len(query) > 100 else query,
        'response_length': len(response),
        'retrieval_results': retrieval_results,
        'intent': intent,
        'language': language,
        'response_time_ms': round(response_time * 1000, 2) if response_time else None,
        'timestamp': time.time(),
    }
    
    logger.info("💬 Chat interaction logged", extra=chat_data)


def log_retrieval_performance(query: str, results_count: int, search_time: float, 
                            embedding_time: float = None, rerank_time: float = None):
    """Log vector retrieval performance metrics."""
    retrieval_data = {
        'activity_type': 'VECTOR_RETRIEVAL',
        'query_length': len(query),
        'query_preview': query[:50] + '...' if len(query) > 50 else query,
        'results_count': results_count,
        'search_time_ms': round(search_time * 1000, 2),
        'embedding_time_ms': round(embedding_time * 1000, 2) if embedding_time else None,
        'rerank_time_ms': round(rerank_time * 1000, 2) if rerank_time else None,
        'timestamp': time.time(),
    }
    
    if results_count == 0:
        logger.warning("🔍 No retrieval results found", extra=retrieval_data)
    else:
        logger.info("🔍 Vector retrieval completed", extra=retrieval_data)


def log_api_call_timing(endpoint: str, duration: float, success: bool = True, error: str = None):
    """Log API call performance and success/failure."""
    timing_data = {
        'activity_type': 'API_TIMING',
        'endpoint': endpoint,
        'duration_ms': round(duration * 1000, 2),
        'success': success,
        'error': error,
        'timestamp': time.time(),
    }
    
    if success:
        logger.info(f"📊 API call completed: {endpoint}", extra=timing_data)
    else:
        logger.error(f"📊 API call failed: {endpoint}", extra=timing_data)


def require_logging(activity_type: str):
    """Decorator to automatically log function calls with timing."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                log_api_call_timing(f"{func.__module__}.{func.__name__}", duration, True)
                return result
            except Exception as e:
                duration = time.time() - start_time
                log_api_call_timing(f"{func.__module__}.{func.__name__}", duration, False, str(e))
                raise
        return wrapper
    return decorator
