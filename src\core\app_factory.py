"""
Flask application factory for the Advanced HR Assistant Chatbot.
Creates and configures the Flask application with all necessary components.
"""

from flask import Flask
from flask_cors import CORS
from src.config import DEBUG_MODE
from src.utils.logger import get_logger

logger = get_logger(__name__)


def create_app():
    """Create and configure the Flask application."""
    try:
        # Create Flask app
        app = Flask(__name__)

        # Configure CORS
        CORS(app, origins=["*"], supports_credentials=True)

        # Configure app settings
        app.config['SECRET_KEY'] = 'your-secret-key-here'  # Change in production
        app.config['DEBUG'] = DEBUG_MODE

        # Register middleware
        from src.middleware.logging_middleware import setup_logging_middleware
        from src.middleware.error_handler import setup_error_handlers
        setup_logging_middleware(app)
        setup_error_handlers(app)

        # Register API blueprints
        from src.api.chat_routes import chat_bp
        from src.api.analytics_routes import analytics_bp
        from src.api.admin_routes import admin_bp
        from src.api.system_routes import system_bp

        app.register_blueprint(chat_bp, url_prefix='/api')
        app.register_blueprint(analytics_bp, url_prefix='/api')
        app.register_blueprint(admin_bp, url_prefix='/api')
        app.register_blueprint(system_bp, url_prefix='/api')

        # Basic health check route
        @app.route('/health')
        def health_check():
            return {'status': 'healthy', 'message': 'HR Assistant Chatbot is running'}

        # API health check route (for frontend)
        @app.route('/api/health')
        def api_health_check():
            return {'status': 'healthy', 'message': 'HR Assistant Chatbot API is running'}

        # Basic root route
        @app.route('/')
        def root():
            return {'message': 'Welcome to HR Assistant Chatbot API'}

        logger.info("Flask application created successfully with all blueprints registered")
        return app

    except Exception as e:
        logger.error(f"Error creating Flask application: {e}")
        raise

