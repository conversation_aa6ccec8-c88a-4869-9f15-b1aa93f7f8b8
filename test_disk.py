#!/usr/bin/env python3
"""
Test disk usage functions.
"""

import psutil
import os

def test_disk_usage():
    """Test disk usage functions."""
    print("Testing disk usage functions...")
    
    try:
        print(f"OS name: {os.name}")
        
        if os.name == 'nt':  # Windows
            print("Testing Windows disk usage...")
            disk = psutil.disk_usage('C:')
            print(f"C: drive - Total: {disk.total}, Free: {disk.free}, Used: {disk.used}")
        else:
            print("Testing Unix disk usage...")
            disk = psutil.disk_usage('/')
            print(f"/ drive - Total: {disk.total}, Free: {disk.free}, Used: {disk.used}")
        
        print("✅ Disk usage test successful")
        
    except Exception as e:
        print(f"❌ Disk usage test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_disk_usage()
