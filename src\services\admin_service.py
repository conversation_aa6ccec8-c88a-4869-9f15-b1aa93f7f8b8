"""
Admin service for managing users, settings, and system administration.
"""

import time
import sqlite3
from datetime import datetime
from typing import Dict, Any, List, Optional
from src.utils.logger import get_logger
from src.config import ADMIN_USERS_DB_PATH

logger = get_logger(__name__)


class AdminService:
    """Service for administrative functions and user management."""
    
    def __init__(self):
        """Initialize the admin service."""
        self._ensure_admin_tables()
        logger.info("AdminService initialized successfully")
    
    def _ensure_admin_tables(self):
        """Ensure admin-related tables exist."""
        try:
            with sqlite3.connect(ADMIN_USERS_DB_PATH) as conn:
                cursor = conn.cursor()
                
                # Admin users table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS admin_users (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        email TEXT NOT NULL UNIQUE,
                        full_name TEXT NOT NULL,
                        role TEXT DEFAULT 'admin',
                        tenant_id TEXT,
                        active INTEGER DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Admin settings table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS admin_settings (
                        key TEXT PRIMARY KEY,
                        value TEXT NOT NULL,
                        description TEXT,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Audit logs table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS audit_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id TEXT,
                        action_type TEXT NOT NULL,
                        resource_type TEXT,
                        resource_id TEXT,
                        details TEXT,
                        ip_address TEXT,
                        user_agent TEXT,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                conn.commit()
                logger.info("Admin tables ensured successfully")
                
        except Exception as e:
            logger.error(f"Error ensuring admin tables: {e}", exc_info=True)
            raise
    
    def get_admin_users(self, limit: int = 50, offset: int = 0, role: Optional[str] = None) -> Dict[str, Any]:
        """Get list of admin users with pagination."""
        try:
            with sqlite3.connect(ADMIN_USERS_DB_PATH) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                # Build query with optional role filter
                query = "SELECT * FROM admin_users WHERE active = 1"
                params = []
                
                if role:
                    query += " AND role = ?"
                    params.append(role)
                
                query += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
                params.extend([limit, offset])
                
                cursor.execute(query, params)
                users = [dict(row) for row in cursor.fetchall()]
                
                # Get total count
                count_query = "SELECT COUNT(*) FROM admin_users WHERE active = 1"
                count_params = []
                if role:
                    count_query += " AND role = ?"
                    count_params.append(role)
                
                cursor.execute(count_query, count_params)
                total_count = cursor.fetchone()[0]
                
                return {
                    'users': users,
                    'total': total_count,
                    'limit': limit,
                    'offset': offset,
                    'timestamp': time.time()
                }
                
        except Exception as e:
            logger.error(f"Error getting admin users: {e}", exc_info=True)
            return {'error': str(e), 'timestamp': time.time()}
    
    def create_admin_user(self, email: str, full_name: str, role: str = 'admin', tenant_id: Optional[str] = None) -> Dict[str, Any]:
        """Create a new admin user."""
        try:
            with sqlite3.connect(ADMIN_USERS_DB_PATH) as conn:
                cursor = conn.cursor()
                
                # Check if user already exists
                cursor.execute("SELECT id FROM admin_users WHERE email = ?", (email,))
                if cursor.fetchone():
                    return {'success': False, 'message': 'User with this email already exists'}
                
                # Insert new user
                cursor.execute('''
                    INSERT INTO admin_users (email, full_name, role, tenant_id)
                    VALUES (?, ?, ?, ?)
                ''', (email, full_name, role, tenant_id))
                
                user_id = cursor.lastrowid
                conn.commit()
                
                # Log the action
                self._log_audit_action('USER_CREATED', 'admin_user', str(user_id), {
                    'email': email,
                    'role': role
                })
                
                return {
                    'success': True,
                    'user_id': user_id,
                    'message': 'Admin user created successfully'
                }
                
        except Exception as e:
            logger.error(f"Error creating admin user: {e}", exc_info=True)
            return {'success': False, 'error': str(e)}
    
    def update_admin_user(self, user_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Update an admin user."""
        try:
            with sqlite3.connect(ADMIN_USERS_DB_PATH) as conn:
                cursor = conn.cursor()
                
                # Check if user exists
                cursor.execute("SELECT id FROM admin_users WHERE id = ?", (user_id,))
                if not cursor.fetchone():
                    return {'success': False, 'message': 'User not found'}
                
                # Build update query
                allowed_fields = ['full_name', 'role', 'tenant_id', 'active']
                update_fields = []
                update_values = []
                
                for field in allowed_fields:
                    if field in data:
                        update_fields.append(f"{field} = ?")
                        update_values.append(data[field])
                
                if not update_fields:
                    return {'success': False, 'message': 'No valid fields to update'}
                
                update_fields.append("updated_at = CURRENT_TIMESTAMP")
                update_values.append(user_id)
                
                query = f"UPDATE admin_users SET {', '.join(update_fields)} WHERE id = ?"
                cursor.execute(query, update_values)
                conn.commit()
                
                # Log the action
                self._log_audit_action('USER_UPDATED', 'admin_user', user_id, data)
                
                return {
                    'success': True,
                    'message': 'Admin user updated successfully'
                }
                
        except Exception as e:
            logger.error(f"Error updating admin user: {e}", exc_info=True)
            return {'success': False, 'error': str(e)}
    
    def change_user_role(self, user_id: str, new_role: str) -> Dict[str, Any]:
        """Change a user's role."""
        try:
            valid_roles = ['admin', 'superadmin', 'viewer', 'hr_lead']
            if new_role not in valid_roles:
                return {'success': False, 'message': f'Invalid role. Must be one of: {valid_roles}'}
            
            with sqlite3.connect(ADMIN_USERS_DB_PATH) as conn:
                cursor = conn.cursor()
                
                # Check if user exists and get current role
                cursor.execute("SELECT role FROM admin_users WHERE id = ?", (user_id,))
                result = cursor.fetchone()
                if not result:
                    return {'success': False, 'message': 'User not found'}
                
                old_role = result[0]
                
                # Update role
                cursor.execute('''
                    UPDATE admin_users 
                    SET role = ?, updated_at = CURRENT_TIMESTAMP 
                    WHERE id = ?
                ''', (new_role, user_id))
                conn.commit()
                
                # Log the action
                self._log_audit_action('ROLE_CHANGED', 'admin_user', user_id, {
                    'old_role': old_role,
                    'new_role': new_role
                })
                
                return {
                    'success': True,
                    'message': f'User role changed from {old_role} to {new_role}'
                }
                
        except Exception as e:
            logger.error(f"Error changing user role: {e}", exc_info=True)
            return {'success': False, 'error': str(e)}
    
    def get_admin_settings(self) -> Dict[str, Any]:
        """Get admin settings."""
        try:
            with sqlite3.connect(ADMIN_USERS_DB_PATH) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute("SELECT * FROM admin_settings ORDER BY key")
                settings = {row['key']: {'value': row['value'], 'description': row['description']} for row in cursor.fetchall()}
                
                # Add default settings if they don't exist
                default_settings = {
                    'max_chat_history': {'value': '100', 'description': 'Maximum chat history to retain'},
                    'enable_analytics': {'value': 'true', 'description': 'Enable analytics collection'},
                    'log_retention_days': {'value': '30', 'description': 'Days to retain logs'},
                    'rate_limit_per_minute': {'value': '60', 'description': 'API rate limit per minute'}
                }
                
                for key, default in default_settings.items():
                    if key not in settings:
                        settings[key] = default
                
                return {
                    'settings': settings,
                    'timestamp': time.time()
                }
                
        except Exception as e:
            logger.error(f"Error getting admin settings: {e}", exc_info=True)
            return {'error': str(e), 'timestamp': time.time()}
    
    def update_admin_settings(self, settings_data: Dict[str, str]) -> Dict[str, Any]:
        """Update admin settings."""
        try:
            with sqlite3.connect(ADMIN_USERS_DB_PATH) as conn:
                cursor = conn.cursor()
                
                updated_count = 0
                for key, value in settings_data.items():
                    cursor.execute('''
                        INSERT OR REPLACE INTO admin_settings (key, value, updated_at)
                        VALUES (?, ?, CURRENT_TIMESTAMP)
                    ''', (key, value))
                    updated_count += 1
                
                conn.commit()
                
                # Log the action
                self._log_audit_action('SETTINGS_UPDATED', 'admin_settings', None, settings_data)
                
                return {
                    'success': True,
                    'updated_count': updated_count,
                    'message': f'Updated {updated_count} settings'
                }
                
        except Exception as e:
            logger.error(f"Error updating admin settings: {e}", exc_info=True)
            return {'success': False, 'error': str(e)}

    def get_audit_logs(self, limit: int = 50, offset: int = 0, start_date: Optional[str] = None,
                      end_date: Optional[str] = None, action_type: Optional[str] = None,
                      user_id: Optional[str] = None) -> Dict[str, Any]:
        """Get audit logs with filtering."""
        try:
            with sqlite3.connect(ADMIN_USERS_DB_PATH) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # Build query with filters
                query = "SELECT * FROM audit_logs WHERE 1=1"
                params = []

                if start_date:
                    query += " AND timestamp >= ?"
                    params.append(start_date)

                if end_date:
                    query += " AND timestamp <= ?"
                    params.append(end_date)

                if action_type:
                    query += " AND action_type = ?"
                    params.append(action_type)

                if user_id:
                    query += " AND user_id = ?"
                    params.append(user_id)

                query += " ORDER BY timestamp DESC LIMIT ? OFFSET ?"
                params.extend([limit, offset])

                cursor.execute(query, params)
                logs = [dict(row) for row in cursor.fetchall()]

                # Get total count
                count_query = "SELECT COUNT(*) FROM audit_logs WHERE 1=1"
                count_params = []

                if start_date:
                    count_query += " AND timestamp >= ?"
                    count_params.append(start_date)

                if end_date:
                    count_query += " AND timestamp <= ?"
                    count_params.append(end_date)

                if action_type:
                    count_query += " AND action_type = ?"
                    count_params.append(action_type)

                if user_id:
                    count_query += " AND user_id = ?"
                    count_params.append(user_id)

                cursor.execute(count_query, count_params)
                total_count = cursor.fetchone()[0]

                return {
                    'logs': logs,
                    'total': total_count,
                    'limit': limit,
                    'offset': offset,
                    'filters': {
                        'start_date': start_date,
                        'end_date': end_date,
                        'action_type': action_type,
                        'user_id': user_id
                    },
                    'timestamp': time.time()
                }

        except Exception as e:
            logger.error(f"Error getting audit logs: {e}", exc_info=True)
            return {'error': str(e), 'timestamp': time.time()}

    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status and health."""
        try:
            # Check various system components
            status_data = {
                'overall_status': 'healthy',
                'timestamp': time.time(),
                'components': {
                    'database': self._check_database_health(),
                    'vector_store': self._check_vector_store_health(),
                    'chat_service': self._check_chat_service_health(),
                    'analytics': self._check_analytics_health()
                },
                'metrics': {
                    'uptime': self._get_uptime(),
                    'memory_usage': self._get_memory_usage(),
                    'disk_usage': self._get_disk_usage()
                }
            }

            # Determine overall status
            component_statuses = [comp.get('status', 'unknown') for comp in status_data['components'].values()]
            if 'unhealthy' in component_statuses:
                status_data['overall_status'] = 'unhealthy'
            elif 'degraded' in component_statuses:
                status_data['overall_status'] = 'degraded'

            return status_data

        except Exception as e:
            logger.error(f"Error getting system status: {e}", exc_info=True)
            return {
                'overall_status': 'unhealthy',
                'error': str(e),
                'timestamp': time.time()
            }

    def get_escalations(self, status: str = 'pending', limit: int = 50, offset: int = 0) -> Dict[str, Any]:
        """Get escalated issues and tickets."""
        try:
            # This would integrate with the escalation system
            # For now, return mock data
            escalations = [
                {
                    'id': 1,
                    'title': 'Payroll Query Issue',
                    'description': 'User unable to access payroll information',
                    'status': 'pending',
                    'priority': 'high',
                    'created_at': '2025-08-10T10:00:00Z',
                    'assigned_to': None
                },
                {
                    'id': 2,
                    'title': 'Leave Policy Clarification',
                    'description': 'Multiple users asking about new leave policy',
                    'status': 'in_progress',
                    'priority': 'medium',
                    'created_at': '2025-08-10T09:30:00Z',
                    'assigned_to': 'hr_team'
                }
            ]

            # Filter by status
            filtered_escalations = [esc for esc in escalations if esc['status'] == status]

            return {
                'escalations': filtered_escalations[offset:offset+limit],
                'total': len(filtered_escalations),
                'limit': limit,
                'offset': offset,
                'status_filter': status,
                'timestamp': time.time()
            }

        except Exception as e:
            logger.error(f"Error getting escalations: {e}", exc_info=True)
            return {'error': str(e), 'timestamp': time.time()}

    def clear_cache(self, cache_type: str = 'all') -> Dict[str, Any]:
        """Clear system caches."""
        try:
            cleared_caches = []

            if cache_type in ['all', 'embeddings']:
                # Clear embedding cache
                cleared_caches.append('embeddings')

            if cache_type in ['all', 'models']:
                # Clear model cache
                cleared_caches.append('models')

            if cache_type in ['all', 'conversations']:
                # Clear conversation cache
                cleared_caches.append('conversations')

            # Log the action
            self._log_audit_action('CACHE_CLEARED', 'system', None, {
                'cache_type': cache_type,
                'cleared_caches': cleared_caches
            })

            return {
                'success': True,
                'cleared_caches': cleared_caches,
                'message': f'Cleared {len(cleared_caches)} cache(s)'
            }

        except Exception as e:
            logger.error(f"Error clearing cache: {e}", exc_info=True)
            return {'success': False, 'error': str(e)}

    def _log_audit_action(self, action_type: str, resource_type: str, resource_id: Optional[str], details: Dict[str, Any]):
        """Log an audit action."""
        try:
            import json
            with sqlite3.connect(ADMIN_USERS_DB_PATH) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO audit_logs (action_type, resource_type, resource_id, details)
                    VALUES (?, ?, ?, ?)
                ''', (action_type, resource_type, resource_id, json.dumps(details)))
                conn.commit()
        except Exception as e:
            logger.error(f"Error logging audit action: {e}", exc_info=True)

    def _check_database_health(self) -> Dict[str, Any]:
        """Check database health."""
        try:
            with sqlite3.connect(ADMIN_USERS_DB_PATH) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
            return {'status': 'healthy', 'message': 'Database connection successful'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': str(e)}

    def _check_vector_store_health(self) -> Dict[str, Any]:
        """Check vector store health."""
        try:
            # This would check Qdrant connection
            return {'status': 'healthy', 'message': 'Vector store operational'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': str(e)}

    def _check_chat_service_health(self) -> Dict[str, Any]:
        """Check chat service health."""
        try:
            # This would check chat service components
            return {'status': 'healthy', 'message': 'Chat service operational'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': str(e)}

    def _check_analytics_health(self) -> Dict[str, Any]:
        """Check analytics service health."""
        try:
            # This would check analytics components
            return {'status': 'healthy', 'message': 'Analytics service operational'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': str(e)}

    def _get_uptime(self) -> str:
        """Get system uptime."""
        # This would calculate actual uptime
        return "2 days, 14 hours, 32 minutes"

    def _get_memory_usage(self) -> Dict[str, Any]:
        """Get memory usage statistics."""
        # This would get actual memory usage
        return {
            'used': '2.1 GB',
            'total': '8.0 GB',
            'percentage': 26.25
        }

    def _get_disk_usage(self) -> Dict[str, Any]:
        """Get disk usage statistics."""
        # This would get actual disk usage
        return {
            'used': '45.2 GB',
            'total': '100.0 GB',
            'percentage': 45.2
        }
