"""
Main application entry point for the Advanced HR Assistant Chatbot.
Refactored for production-grade architecture with modular design.
"""

import signal
import sys
import threading
import multiprocessing

# Import the application factory and startup functions
from src.core.app_factory import create_app
from src.core.startup import initialize_services, setup_signal_handlers, setup_multiprocessing
from src.utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)


def main():
    """Main application entry point."""
    try:
        print("🚀 Starting server initialization...")

        # Setup signal handlers and multiprocessing
        setup_signal_handlers()
        setup_multiprocessing()

        # Initialize services
        service_manager = initialize_services()
        print("✅ Services initialized successfully")

        # Create Flask app
        flask_app = create_app()
        print("✅ Flask app created successfully")

        print("🌐 Starting Flask server on port 5051...")
        flask_app.run(host="0.0.0.0", port=5051, debug=False)

    except Exception as e:
        print(f"❌ Error starting server: {e}")
        import traceback
        traceback.print_exc()
        logger.error(f"Error starting server: {e}")
        raise


if __name__ == "__main__":
    main()
