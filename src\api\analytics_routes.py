"""
Analytics API routes for the HR Assistant Chatbot.
Provides real-time analytics, metrics, and performance data for the admin dashboard.
"""

import time
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, g
from src.utils.logger import get_logger
from src.middleware.logging_middleware import log_user_activity, require_logging
from src.services.analytics_service import AnalyticsService

logger = get_logger(__name__)
analytics_bp = Blueprint('analytics', __name__)

# Initialize analytics service
analytics_service = AnalyticsService()


@analytics_bp.route('/chat-analytics/live', methods=['GET'])
@require_logging('LIVE_ANALYTICS')
def get_live_analytics():
    """Get real-time chat analytics for the dashboard."""
    try:
        log_user_activity('LIVE_ANALYTICS_REQUEST')
        
        # Get time range from query parameters
        time_range = request.args.get('time_range', '24h')
        
        analytics_data = analytics_service.get_live_analytics(time_range)
        
        return jsonify(analytics_data)
        
    except Exception as e:
        logger.error(f"Error retrieving live analytics: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve live analytics',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@analytics_bp.route('/metrics/<metric_name>', methods=['GET'])
@require_logging('METRICS')
def get_metrics(metric_name):
    """Get specific metrics by name."""
    try:
        time_range = request.args.get('time_range', '24h')
        
        log_user_activity('METRICS_REQUEST', {
            'metric_name': metric_name,
            'time_range': time_range
        })
        
        metrics_data = analytics_service.get_metrics(metric_name, time_range)
        
        if metrics_data is None:
            return jsonify({'error': f'Metric {metric_name} not found'}), 404
        
        return jsonify(metrics_data)
        
    except Exception as e:
        logger.error(f"Error retrieving metrics {metric_name}: {e}", exc_info=True)
        return jsonify({
            'error': f'Failed to retrieve metrics for {metric_name}',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@analytics_bp.route('/user-metrics', methods=['GET'])
@require_logging('USER_METRICS')
def get_user_metrics():
    """Get user engagement and usage metrics."""
    try:
        time_range = request.args.get('time_range', '7d')
        
        log_user_activity('USER_METRICS_REQUEST', {
            'time_range': time_range
        })
        
        user_metrics = analytics_service.get_user_metrics(time_range)
        
        return jsonify(user_metrics)
        
    except Exception as e:
        logger.error(f"Error retrieving user metrics: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve user metrics',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@analytics_bp.route('/chatlogs', methods=['GET'])
@require_logging('CHATLOGS')
def get_chatlogs():
    """Get chat logs with filtering and pagination."""
    try:
        # Get query parameters
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        device_id = request.args.get('device_id')
        intent = request.args.get('intent')
        
        # Validate limit
        if limit > 100:
            limit = 100
        
        log_user_activity('CHATLOGS_REQUEST', {
            'limit': limit,
            'offset': offset,
            'start_date': start_date,
            'end_date': end_date,
            'device_id': device_id,
            'intent': intent
        })
        
        chatlogs = analytics_service.get_chatlogs(
            limit=limit,
            offset=offset,
            start_date=start_date,
            end_date=end_date,
            device_id=device_id,
            intent=intent
        )
        
        return jsonify(chatlogs)
        
    except Exception as e:
        logger.error(f"Error retrieving chatlogs: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve chatlogs',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@analytics_bp.route('/chat-trends', methods=['GET'])
@require_logging('CHAT_TRENDS')
def get_chat_trends():
    """Get chat trends and patterns over time."""
    try:
        time_range = request.args.get('time_range', '7d')
        
        log_user_activity('CHAT_TRENDS_REQUEST', {
            'time_range': time_range
        })
        
        trends_data = analytics_service.get_chat_trends(time_range)
        
        return jsonify(trends_data)
        
    except Exception as e:
        logger.error(f"Error retrieving chat trends: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve chat trends',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@analytics_bp.route('/intent-analytics', methods=['GET'])
@require_logging('INTENT_ANALYTICS')
def get_intent_analytics():
    """Get intent classification analytics."""
    try:
        time_range = request.args.get('time_range', '7d')
        
        log_user_activity('INTENT_ANALYTICS_REQUEST', {
            'time_range': time_range
        })
        
        intent_data = analytics_service.get_intent_analytics(time_range)
        
        return jsonify(intent_data)
        
    except Exception as e:
        logger.error(f"Error retrieving intent analytics: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve intent analytics',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@analytics_bp.route('/performance-metrics', methods=['GET'])
@require_logging('PERFORMANCE_METRICS')
def get_performance_metrics():
    """Get system performance metrics."""
    try:
        time_range = request.args.get('time_range', '24h')
        
        log_user_activity('PERFORMANCE_METRICS_REQUEST', {
            'time_range': time_range
        })
        
        performance_data = analytics_service.get_performance_metrics(time_range)
        
        return jsonify(performance_data)
        
    except Exception as e:
        logger.error(f"Error retrieving performance metrics: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve performance metrics',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@analytics_bp.route('/sentiment-analysis', methods=['GET'])
@require_logging('SENTIMENT_ANALYSIS')
def get_sentiment_analysis():
    """Get sentiment analysis of chat interactions."""
    try:
        time_range = request.args.get('time_range', '7d')
        
        log_user_activity('SENTIMENT_ANALYSIS_REQUEST', {
            'time_range': time_range
        })
        
        sentiment_data = analytics_service.get_sentiment_analysis(time_range)
        
        return jsonify(sentiment_data)
        
    except Exception as e:
        logger.error(f"Error retrieving sentiment analysis: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve sentiment analysis',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500


@analytics_bp.route('/retrieval-analytics', methods=['GET'])
@require_logging('RETRIEVAL_ANALYTICS')
def get_retrieval_analytics():
    """Get vector retrieval performance analytics."""
    try:
        time_range = request.args.get('time_range', '24h')
        
        log_user_activity('RETRIEVAL_ANALYTICS_REQUEST', {
            'time_range': time_range
        })
        
        retrieval_data = analytics_service.get_retrieval_analytics(time_range)
        
        return jsonify(retrieval_data)
        
    except Exception as e:
        logger.error(f"Error retrieving retrieval analytics: {e}", exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve retrieval analytics',
            'message': str(e),
            'request_id': getattr(g, 'request_id', 'unknown')
        }), 500
